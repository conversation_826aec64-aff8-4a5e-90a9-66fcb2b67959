/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.emc.it.eis.gemfire.lookup.service.test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.zip.GZIPOutputStream;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.util.ReflectionTestUtils;

import com.emc.it.eis.gemfire.lookup.service.controller.ClaimCheckController;
import com.emc.it.eis.gemfire.lookup.service.gemfire.CicClaimchecks;
import com.emc.it.eis.gemfire.lookup.service.gemfire.ClaimCheckRepository;

@ExtendWith(SpringExtension.class)
//@SpringApplicationConfiguration(classes = Application.class)
@WebAppConfiguration
@ActiveProfiles({"test"})
@PropertySource("classpath:db-test.properties")
class ClaimCheckControllerTests {

	private static final Logger logger = LoggerFactory.getLogger(ClaimCheckControllerTests.class);

	ClaimCheckController claimCheckController;
	ClaimCheckRepository claimCheckRepository;
	CicClaimchecks cicClaimchecks;

	private static String payload = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><msg:Message xmlns:msg=\"http://emc.com/it/enterprise/msg/v1\">Payload present found for claim check id provided</msg:Message>";

	private static String noPayload = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><msg:Message xmlns:msg=\"http://emc.com/it/enterprise/msg/v1\">No data found for claim check id provided</msg:Message>";

	@BeforeEach
	void setUp() throws IOException {
		claimCheckController = new ClaimCheckController();
		claimCheckRepository = Mockito.mock(ClaimCheckRepository.class);
		System.out.println("Claim check reposiroty: " + claimCheckRepository);
		ReflectionTestUtils.setField(claimCheckController, "claimCheckRepository",
				claimCheckRepository);

		cicClaimchecks = new CicClaimchecks();
		cicClaimchecks.setId("1234");
		cicClaimchecks.setPayload(getTestData());

	}

	@Test
	void dataExists() throws ClassNotFoundException, IOException {
		when(claimCheckRepository.findById("1234")).thenReturn(Optional.of(cicClaimchecks));
		String xmlData = claimCheckController.getClaimCheckpayload("1234");
		assertNotNull(xmlData);
	}

	@Test
	void dataNotExists() throws ClassNotFoundException, IOException {
		when(claimCheckRepository.findById("1234"))
				.thenReturn(Optional.of(cicClaimchecks));
		String xmlData = claimCheckController.getClaimCheckpayload("123");
		assertNotNull(xmlData);
		assertEquals(xmlData, noPayload);
	}

	private byte[] getTestData() {
		try {
			Map<String, String> mapObject = new HashMap<>();
			mapObject.put("payload", payload);

			ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
			GZIPOutputStream zipStream;

			zipStream = new GZIPOutputStream(byteOut);

			ObjectOutputStream outputStream = new ObjectOutputStream(zipStream);
			outputStream.writeObject(mapObject);
			outputStream.close();
			zipStream.close();
			byteOut.close();
			byte[] compressedData = byteOut.toByteArray();
			logger.info("compressed data: {}", compressedData);

			return compressedData;
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
}
