package com.emc.it.eis.gemfire.lookup.service.cicadm;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

@Entity

@Table(name = "cic_lookup_key_col")
public class LookupKeyCol {

	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cic_lookup_kc_sequence")
	@SequenceGenerator(name = "cic_lookup_kc_sequence", sequenceName="cic_lookup_kc_sequence", allocationSize=1, initialValue=1000)
	@Column(name = "id")
	private int id;
	@Column(name = "lookup_name")
	private String lookupName;
	@Column(name = "column_alias")
	private String columnAlias;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getLookupName() {
		return lookupName;
	}

	public void setLookupName(String lookupName) {
		this.lookupName = lookupName;
	}

	public String getColumnAlias() {
		return columnAlias;
	}

	public void setColumnAlias(String columnAlias) {
		this.columnAlias = columnAlias;
	}

}
