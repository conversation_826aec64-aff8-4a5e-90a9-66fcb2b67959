cicadm.db.url=************************************
cicadm.db.username=sa
cictrans.db.url=************************************
cictrans.db.username=sa
gemfire.db.url=************************************
gemfire.db.username=sa
jdbc.driverClassName=org.hsqldb.jdbcDriver
spring.jpa.database-platform=org.hibernate.dialect.HSQLDialect
spring.jpa.hibernate.ddl-auto=create
hibernate.hbm2ddl=auto
hibernate.show_sql=true
gemfire.lookup.service.security.username=aicrestuser
gemfire.lookup.service.security.enabled=true
gemfire.lookup.service.security.csrf.enabled=false
gemfire.lookup.service.security.skip.url.pattern= /(application.wadl|health)
gemfire.lookup.service.security.allowOrigin= swaggerui.cf.isus.emc.com
gemfire.lookup.service.cache=session

