/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.emc.it.eis.gemfire.lookup.service.test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer.MethodName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.util.ReflectionTestUtils;

import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadata;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadataRepository;
import com.emc.it.eis.gemfire.lookup.service.cictrans.LookupService;
import com.emc.it.eis.gemfire.lookup.service.controller.LookupController;
import com.emc.it.eis.gemfire.lookup.service.security.InputValidator;

@ExtendWith(SpringExtension.class)
@WebAppConfiguration
@PropertySource("classpath:db-test.properties")
@ActiveProfiles({"test"})
@TestMethodOrder(MethodName.class)
class LookupControllerTests {

	LookupController lookupController;

	private LookupMetadataRepository lookupMetadataRepository;

	private LookupService lookupService;

	private InputValidator inputValidator;

	private JdbcTemplate jdbcTemplate;
	private DataSource dataSource;
	private Connection connection;
	private DatabaseMetaData metadata;
	private ResultSet resultSet;

	private LookupMetadata lookupMetadata;

	Map<String, Object> data;
	List<Map<String, Object>> listMap;

	@BeforeEach
	void setUp() throws IOException, SQLException {
		lookupController = new LookupController();
		lookupService = new LookupService();

		jdbcTemplate = Mockito.mock(JdbcTemplate.class);
		dataSource = Mockito.mock(DataSource.class);
		connection = Mockito.mock(Connection.class);
		metadata = Mockito.mock(DatabaseMetaData.class);
		resultSet = Mockito.mock(ResultSet.class);

		lookupMetadataRepository = Mockito.mock(LookupMetadataRepository.class);
		inputValidator = Mockito.mock(InputValidator.class);

		when(jdbcTemplate.getDataSource()).thenReturn(dataSource);
		when(dataSource.getConnection()).thenReturn(connection);
		when(connection.getMetaData()).thenReturn(metadata);
		when(metadata.getUserName()).thenReturn("test");

		when(metadata.getColumns(Mockito.any(), Mockito.any(), Mockito.anyString(), Mockito.any()))
				.thenReturn(resultSet);

		when(resultSet.next()).thenReturn(false);

		// Mock InputValidator to return valid inputs for test cases
		when(inputValidator.validateAndSanitizeLookupName(Mockito.anyString())).thenAnswer(invocation -> invocation.getArgument(0));
		when(inputValidator.containsSqlInjectionPattern(Mockito.anyString())).thenReturn(false);

		ReflectionTestUtils.setField(lookupService, "cictransJdbcTemplate", jdbcTemplate);
		ReflectionTestUtils.setField(lookupController, "lookupService", lookupService);
		ReflectionTestUtils.setField(lookupController, "lookupMetadataRepository", lookupMetadataRepository);
		ReflectionTestUtils.setField(lookupController, "inputValidator", inputValidator);
		

		data = new HashMap<>();
		data.put("Column1", "value1");
		data.put("Column2", "value2");
		data.put("old_Column1", "value1");
		data.put("old_Column2", "value2");
		listMap = new ArrayList<>();
		listMap.add(data);

		lookupMetadata = new LookupMetadata();
		lookupMetadata.setLookupName("Lookup1");
		lookupMetadata.setSql("select * from Lookup1");
		lookupMetadata.setTableName("Table1");

	}


	@Test
	void addLookupData() throws Exception {
		when(lookupMetadataRepository.findByLookupName(Mockito.anyString())).thenReturn(lookupMetadata);
		Mockito.doNothing().when(jdbcTemplate).execute(Mockito.anyString());
		HttpStatus httpStatusResult = lookupController.addLookupData(data, "Lookup1", 1234);

		assertNotNull(httpStatusResult);

		assertEquals(HttpStatus.CREATED, httpStatusResult);
	}

	@Test
	void updateLookupData() throws Exception {
		when(lookupMetadataRepository.findByLookupName(Mockito.anyString())).thenReturn(lookupMetadata);
		Mockito.doNothing().when(jdbcTemplate).execute(Mockito.anyString());
		HttpStatus httpStatusResult = lookupController.updateLookupData(data, "Lookup1", 1234);
		assertNotNull(httpStatusResult);
		assertEquals(HttpStatus.CREATED, httpStatusResult);
	}

	@Test
	void deleteLookupData() throws Exception {
		when(lookupMetadataRepository.findByLookupName(Mockito.anyString())).thenReturn(lookupMetadata);
		Mockito.doNothing().when(jdbcTemplate).execute(Mockito.anyString());
		HttpStatus httpStatusResult = lookupController.deleteLookupData(data, "Lookup1");
		assertNotNull(httpStatusResult);
		assertEquals(HttpStatus.ACCEPTED, httpStatusResult);
	}

}
