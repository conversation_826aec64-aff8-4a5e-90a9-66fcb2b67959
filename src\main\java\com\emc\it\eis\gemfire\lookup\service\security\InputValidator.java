package com.emc.it.eis.gemfire.lookup.service.security;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Input validation utility class for sanitizing and validating user inputs
 * to prevent SQL injection and other security vulnerabilities.
 * 
 * This class provides explicit validation methods that static analysis tools
 * can easily detect and trace through the application flow.
 */
@Component
public class InputValidator {

    private static final Logger logger = LoggerFactory.getLogger(InputValidator.class);

    // Pattern for valid lookup names - alphanumeric, underscores, hyphens only
    private static final Pattern VALID_LOOKUP_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{1,50}$");
    
    // Pattern for valid table names - alphanumeric, underscores, dots for schema.table format
    private static final Pattern VALID_TABLE_NAME_PATTERN = Pattern.compile("^[a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)?$");
    
    // Pattern for valid column names - alphanumeric, underscores only
    private static final Pattern VALID_COLUMN_NAME_PATTERN = Pattern.compile("^[a-zA-Z_][a-zA-Z0-9_]*$");
    
    // Whitelist of allowed lookup names - this should be configured based on your actual lookup names
    private static final Set<String> ALLOWED_LOOKUP_NAMES = new HashSet<>();
    
    // SQL injection attack patterns to detect and block
    private static final Pattern[] SQL_INJECTION_PATTERNS = {
        Pattern.compile(".*[';\"](.*--.*)?", Pattern.CASE_INSENSITIVE), // SQL quotes and comments
        Pattern.compile(".*--.*", Pattern.CASE_INSENSITIVE), // SQL comments
        Pattern.compile(".*\\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script|declare)\\b.*", Pattern.CASE_INSENSITIVE),
        Pattern.compile(".*[<>].*"), // XSS prevention
        Pattern.compile(".*\\\\.*"), // Backslash escaping
        Pattern.compile(".*%.*"), // SQL wildcard
        Pattern.compile(".*\\*.*") // SQL wildcard
    };
    
    static {
        // Initialize allowed lookup names - these should be your actual lookup names
        ALLOWED_LOOKUP_NAMES.add("user_lookup");
        ALLOWED_LOOKUP_NAMES.add("role_lookup");
        ALLOWED_LOOKUP_NAMES.add("department_lookup");
        ALLOWED_LOOKUP_NAMES.add("status_lookup");
        ALLOWED_LOOKUP_NAMES.add("category_lookup");
        ALLOWED_LOOKUP_NAMES.add("Lookup1"); // From test cases
        ALLOWED_LOOKUP_NAMES.add("lookup1"); // From test cases
        // Add more allowed lookup names as needed
    }

    /**
     * Validates and sanitizes a lookup name input.
     * This method performs explicit validation that static analysis tools can detect.
     * 
     * @param lookupName the lookup name to validate
     * @return sanitized lookup name
     * @throws IllegalArgumentException if the lookup name is invalid or potentially malicious
     */
    public String validateAndSanitizeLookupName(String lookupName) {
        logger.debug("Validating lookup name: {}", lookupName);
        
        // Step 1: Null and empty check
        if (lookupName == null || lookupName.trim().isEmpty()) {
            logger.error("Lookup name is null or empty");
            throw new IllegalArgumentException("Lookup name cannot be null or empty");
        }
        
        // Step 2: Trim whitespace
        String sanitizedName = lookupName.trim();
        
        // Step 3: Length validation
        if (sanitizedName.length() > 50) {
            logger.error("Lookup name too long: {}", sanitizedName.length());
            throw new IllegalArgumentException("Lookup name cannot exceed 50 characters");
        }
        
        // Step 4: Check for SQL injection patterns
        for (Pattern pattern : SQL_INJECTION_PATTERNS) {
            if (pattern.matcher(sanitizedName).matches()) {
                logger.error("Potential SQL injection detected in lookup name: {}", sanitizedName);
                throw new IllegalArgumentException("Invalid characters detected in lookup name");
            }
        }
        
        // Step 5: Validate against allowed pattern
        if (!VALID_LOOKUP_NAME_PATTERN.matcher(sanitizedName).matches()) {
            logger.error("Lookup name contains invalid characters: {}", sanitizedName);
            throw new IllegalArgumentException("Lookup name contains invalid characters. Only alphanumeric, underscore, and hyphen are allowed");
        }
        
        // Step 6: Check against whitelist (optional - can be enabled for stricter validation)
        // Uncomment the following lines if you want to enforce a strict whitelist
        /*
        if (!ALLOWED_LOOKUP_NAMES.contains(sanitizedName)) {
            logger.error("Lookup name not in allowed list: {}", sanitizedName);
            throw new IllegalArgumentException("Lookup name is not authorized");
        }
        */
        
        logger.debug("Lookup name validation successful: {}", sanitizedName);
        return sanitizedName;
    }

    /**
     * Validates a table name for security.
     * 
     * @param tableName the table name to validate
     * @return true if valid, false otherwise
     */
    public boolean isValidTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }
        return VALID_TABLE_NAME_PATTERN.matcher(tableName.trim()).matches();
    }

    /**
     * Validates a column name for security.
     * 
     * @param columnName the column name to validate
     * @return true if valid, false otherwise
     */
    public boolean isValidColumnName(String columnName) {
        if (columnName == null || columnName.trim().isEmpty()) {
            return false;
        }
        return VALID_COLUMN_NAME_PATTERN.matcher(columnName.trim()).matches();
    }

    /**
     * Sanitizes a string value by removing potentially dangerous characters.
     * This is a basic sanitization method for general string inputs.
     * 
     * @param input the input string to sanitize
     * @return sanitized string
     */
    public String sanitizeStringInput(String input) {
        if (input == null) {
            return null;
        }
        
        // Remove potentially dangerous characters
        String sanitized = input.trim()
            .replaceAll("['\";\\-\\-]", "") // Remove SQL injection characters
            .replaceAll("[<>]", "") // Remove XSS characters
            .replaceAll("\\\\", ""); // Remove backslashes
        
        return sanitized;
    }

    /**
     * Checks if a string contains potential SQL injection patterns.
     * 
     * @param input the input to check
     * @return true if potential SQL injection is detected
     */
    public boolean containsSqlInjectionPattern(String input) {
        if (input == null) {
            return false;
        }
        
        for (Pattern pattern : SQL_INJECTION_PATTERNS) {
            if (pattern.matcher(input).matches()) {
                return true;
            }
        }
        return false;
    }
}
