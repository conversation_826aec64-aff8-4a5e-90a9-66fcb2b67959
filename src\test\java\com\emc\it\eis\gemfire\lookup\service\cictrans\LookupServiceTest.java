package com.emc.it.eis.gemfire.lookup.service.cictrans;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;

@ExtendWith(MockitoExtension.class)
public class LookupServiceTest {

    @Mock
    private JdbcTemplate cictransJdbcTemplate;

    @InjectMocks
    private LookupService lookupService;

    @Test
    public void testDeleteLookupData_ValidTableNameAndLookupData() throws SQLException {
        // Arrange
        String lookupTableName = "table1";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("key1", "value1");
        lookupData.put("key2", "value2");

        // Act
        HttpStatus result = lookupService.deleteLookupData(lookupTableName, lookupData);

        // Assert
        assertEquals(HttpStatus.ACCEPTED, result);
    }

    @Test
    public void testDeleteLookupData_InvalidTableName() throws SQLException {
        // Arrange
        String lookupTableName = "invalid_table";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("key1", "value1");
        lookupData.put("key2", "value2");

        // Act
        HttpStatus result = lookupService.deleteLookupData(lookupTableName, lookupData);

        // Assert
        assertEquals(HttpStatus.ACCEPTED, result);
    }

    @Test
    public void testDeleteLookupData_NullLookupData() throws SQLException {
        // Arrange
        String lookupTableName = "table1";
        Map<String, Object> lookupData = null;

        // Act
        HttpStatus result = lookupService.deleteLookupData(lookupTableName, lookupData);

        // Assert
        assertEquals(HttpStatus.ACCEPTED, result);
    }

    @Test
    public void testDeleteLookupData_EmptyLookupData() throws SQLException {
        // Arrange
        String lookupTableName = "table1";
        Map<String, Object> lookupData = new HashMap<>();

        // Act
        HttpStatus result = lookupService.deleteLookupData(lookupTableName, lookupData);

        // Assert
        assertEquals(HttpStatus.ACCEPTED, result);
    }

    @Test
    public void testDeleteLookupData_SQLException() throws SQLException {
        // Arrange
        String lookupTableName = "table1";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("key1", "value1");
        lookupData.put("key2", "value2");
        doNothing().when(cictransJdbcTemplate).execute(anyString());

        // Act
        HttpStatus result = lookupService.deleteLookupData(lookupTableName, lookupData);

        // Assert
        assertEquals(HttpStatus.ACCEPTED, result);
    }
}