package com.emc.it.eis.gemfire.lookup.service.controller;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.HashMap;
import java.util.Optional;
import java.util.zip.GZIPInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.HtmlUtils;

import com.emc.it.eis.gemfire.lookup.service.gemfire.CicClaimchecks;
import com.emc.it.eis.gemfire.lookup.service.gemfire.ClaimCheckRepository;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

@RestController
public class ClaimCheckController {

	@Autowired
	private ClaimCheckRepository claimCheckRepository;

	private static Logger logger = LoggerFactory.getLogger(ClaimCheckController.class);
	private static String noPayload = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><msg:Message xmlns:msg=\"http://emc.com/it/enterprise/msg/v1\">No data found for claim check id provided</msg:Message>";

	@Operation(summary = "getClaimCheckPayload", description = "getPayload")
	@SuppressWarnings({"unchecked"})
	@GetMapping(value = "/getClaimChecks/{claimCheckId}", produces = {
			"application/xml", "text/xml"})

	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "success", content = @Content(schema = @Schema(implementation = String.class))),
		@ApiResponse(responseCode = "401", description = "Unauthorized"),
		@ApiResponse(responseCode = "404", description = "Not Found"),
		@ApiResponse(responseCode = "403", description = "Forbidden"),
		@ApiResponse(responseCode = "500", description = "Failure") 
	})
	@Transactional("gemfireTransactionManager")
	public String getClaimCheckpayload(@PathVariable String claimCheckId)
			throws IOException, ClassNotFoundException {

		logger.info(" claimCheckRepository:  {}", claimCheckRepository);
		CicClaimchecks cicClaimchecks = null;
		Optional<CicClaimchecks> cicClaimchecksById = claimCheckRepository.findById(claimCheckId);

		if (cicClaimchecksById.isPresent()) {
			cicClaimchecks = cicClaimchecksById.get();
		}
		if (cicClaimchecks != null) {
			logger.debug("finding data from repo {}", cicClaimchecks.getPayload());
			GZIPInputStream zipIn = new GZIPInputStream(new ByteArrayInputStream(cicClaimchecks.getPayload()));
			ObjectInputStream obIn = new SafeObjectInputStream(zipIn);
			HashMap<String, String> payloadObject = (HashMap<String, String>) obIn.readObject();
			String payload=payloadObject.get("payload");
			logger.debug("Payload: {}", payload);
			String sanitizedPayload = HtmlUtils.htmlEscape(payload);
			logger.debug("sanitizedPayload: {}", sanitizedPayload);
			return sanitizedPayload;
		} else {
			return noPayload;
		}

	}


}
