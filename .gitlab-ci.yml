variables:
  SONAR_EXCLUSIONS: '**/Application.java,**/config/*.java,**/HomeController.java,**/JsonHelper.java,**/SwaggerConfiguration.java,**/SecurityConfig.java,**/utils/*.java'
  DEV_PCF_ORG: AICCoreDevOrg
  DEV_PCF_SPACE: aicshd-cit-dev
  PCF_MF_FILE_DEV: manifest-dev-oauth-dtc.yml
  PCF_MF_FILE_DES: manifest-sds-des-dtc.yml
  PCF_MF_FILE_QAS: manifest-cnieap-qas-oauth-dtc.yml
  PCF_MF_FILE_QAX: manifest-qax-oauth-dtc.yml
  PCF_MF_FILE_QAV: manifest-sds-qav-oauth-dtc.yml
  PCF_MF_FILE_PERF: manifest-prf-oauth-dtc.yml
  PCF_MF_FILE_PROD: manifest-prod-oauth2-dtc.yml
  PCF_MF_FILE_DR: manifest-prod-oauth2-dtc.yml
  manifest_appid: 36897
  manifest_appid_prod: 80738
  Actmon_logging_clientrmq: 'no'
  Package_Type: war
  ContivoSupport: 'no'
  Gemfire_connectivity: 'no'
  DEV_KOB_SPACE: aicshd-cit-dev
  NAMESPACE: aicshd-cit-dev
  HELM_NAMESPACE: aicshd-cit-dev
  VAULT_CONFIG_PATH_DEV: "kv/KOB/AT1-NP"
  KOB_VALUES_FILE_DEV: values-dev-oauth-dtc.yml
  KOB_VALUES_FILE_DES: values-sds-des-dtc.yml
  KOB_VALUES_FILE_QAS: values-cnieap-qas-oauth-dtc.yml
  KOB_VALUES_FILE_QAX: values-qax-oauth-dtc.yml
  KOB_VALUES_FILE_QAV: values-sds-qav-oauth-dtc.yml
  KOB_VALUES_FILE_PERF: values-prf-oauth-dtc.yml
  KOB_VALUES_FILE_PROD: values-prod-oauth2-dtc.yml
  KOB_VALUES_FILE_DR: values-prod-oauth2-dtc.yml
include:
- project: ServiceAICNP/shared-template
  ref: v3.0-aic-kob
  file: aic/aic-jobs-cloudnative-kob.yml
