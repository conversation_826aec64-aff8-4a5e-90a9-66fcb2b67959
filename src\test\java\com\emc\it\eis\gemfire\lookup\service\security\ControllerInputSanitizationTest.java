package com.emc.it.eis.gemfire.lookup.service.security;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.reset;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadata;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadataRepository;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadataService;
import com.emc.it.eis.gemfire.lookup.service.cictrans.LookupService;
import com.emc.it.eis.gemfire.lookup.service.controller.LookupController;
import com.emc.it.eis.gemfire.lookup.service.controller.LookupMetadataController;

/**
 * Tests to verify that controller-level input sanitization properly prevents SQL injection
 * and satisfies static analysis tool requirements.
 */
@ExtendWith(MockitoExtension.class)
public class ControllerInputSanitizationTest {

    @Mock
    private LookupService lookupService;
    
    @Mock
    private LookupMetadataService lookupMetadataService;
    
    @Mock
    private LookupMetadataRepository lookupMetadataRepository;
    
    @Mock
    private InputValidator inputValidator;
    
    @InjectMocks
    private LookupController lookupController;
    
    @InjectMocks
    private LookupMetadataController lookupMetadataController;

    private LookupMetadata mockLookupMetadata;

    @BeforeEach
    void setUp() throws Exception {
        mockLookupMetadata = new LookupMetadata();
        mockLookupMetadata.setLookupName("test_lookup");
        mockLookupMetadata.setTableName("test_table");
        mockLookupMetadata.setSql("SELECT * FROM test_table");

        // Setup lenient mocks to avoid unnecessary stubbing exceptions
        lenient().when(lookupMetadataRepository.findByLookupName(anyString())).thenReturn(mockLookupMetadata);
        lenient().when(lookupService.getColumnsCheck(anyString(), any(), anyLong())).thenAnswer(invocation -> invocation.getArgument(1));
        lenient().when(lookupService.insertLookupData(anyString(), any())).thenReturn(HttpStatus.CREATED);
        lenient().when(lookupService.updateLookupData(anyString(), any(), any())).thenReturn(HttpStatus.CREATED);
        lenient().when(lookupService.deleteLookupData(anyString(), any())).thenReturn(HttpStatus.ACCEPTED);

        // Setup default behavior for InputValidator - will be overridden in specific tests
        lenient().when(inputValidator.validateAndSanitizeLookupName(anyString())).thenAnswer(invocation -> {
            String input = invocation.getArgument(0);
            if (input != null && input.matches("^[a-zA-Z0-9_-]{1,50}$")) {
                return input.trim();
            }
            throw new IllegalArgumentException("Invalid lookup name");
        });

        lenient().when(inputValidator.containsSqlInjectionPattern(anyString())).thenAnswer(invocation -> {
            String input = invocation.getArgument(0);
            return input != null && (input.contains("'") || input.contains(";") || input.contains("--"));
        });
    }

    @Test
    void testAddLookupData_ValidInput_Success() throws Exception {
        // Test with valid lookup name
        String validLookupName = "test_lookup";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("column1", "value1");
        
        HttpStatus result = lookupController.addLookupData(lookupData, validLookupName, 1L);
        
        assertEquals(HttpStatus.CREATED, result);
        verify(inputValidator).validateAndSanitizeLookupName(validLookupName);
        verify(inputValidator).containsSqlInjectionPattern(validLookupName);
    }

    @Test
    void testAddLookupData_SqlInjectionAttempt_ThrowsException() {
        // Test with malicious lookup name containing SQL injection
        String maliciousLookupName = "test'; DROP TABLE users; --";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("column1", "value1");

        // Configure mock to detect SQL injection - reset the mock for this specific test
        reset(inputValidator);
        when(inputValidator.validateAndSanitizeLookupName(maliciousLookupName))
            .thenThrow(new IllegalArgumentException("Invalid lookup name"));

        assertThrows(IllegalArgumentException.class, () -> {
            lookupController.addLookupData(lookupData, maliciousLookupName, 1L);
        });

        verify(inputValidator).validateAndSanitizeLookupName(maliciousLookupName);
    }

    @Test
    void testUpdateLookupData_ValidInput_Success() throws Exception {
        String validLookupName = "test_lookup";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("column1", "newValue");
        lookupData.put("old_column1", "oldValue");
        
        HttpStatus result = lookupController.updateLookupData(lookupData, validLookupName, 1L);
        
        assertEquals(HttpStatus.CREATED, result);
        verify(inputValidator).validateAndSanitizeLookupName(validLookupName);
        verify(inputValidator).containsSqlInjectionPattern(validLookupName);
    }

    @Test
    void testUpdateLookupData_SqlInjectionAttempt_ThrowsException() {
        String maliciousLookupName = "test' UNION SELECT password FROM users --";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("column1", "value1");

        // Reset mock for this specific test
        reset(inputValidator);
        when(inputValidator.validateAndSanitizeLookupName(maliciousLookupName))
            .thenThrow(new IllegalArgumentException("Invalid lookup name"));

        assertThrows(IllegalArgumentException.class, () -> {
            lookupController.updateLookupData(lookupData, maliciousLookupName, 1L);
        });

        verify(inputValidator).validateAndSanitizeLookupName(maliciousLookupName);
    }

    @Test
    void testDeleteLookupData_ValidInput_Success() throws Exception {
        String validLookupName = "test_lookup";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("column1", "value1");
        
        HttpStatus result = lookupController.deleteLookupData(lookupData, validLookupName);
        
        assertEquals(HttpStatus.ACCEPTED, result);
        verify(inputValidator).validateAndSanitizeLookupName(validLookupName);
        verify(inputValidator).containsSqlInjectionPattern(validLookupName);
    }

    @Test
    void testDeleteLookupData_SqlInjectionAttempt_ThrowsException() {
        String maliciousLookupName = "test'; DELETE FROM users; --";
        Map<String, Object> lookupData = new HashMap<>();
        lookupData.put("column1", "value1");

        // Reset mock for this specific test
        reset(inputValidator);
        when(inputValidator.validateAndSanitizeLookupName(maliciousLookupName))
            .thenThrow(new IllegalArgumentException("Invalid lookup name"));

        assertThrows(IllegalArgumentException.class, () -> {
            lookupController.deleteLookupData(lookupData, maliciousLookupName);
        });

        verify(inputValidator).validateAndSanitizeLookupName(maliciousLookupName);
    }

    @Test
    void testGetLookupData_ValidInput_Success() throws Exception {
        String validLookupName = "test_lookup";

        // Reset and configure mocks for this specific test
        reset(inputValidator);
        when(inputValidator.validateAndSanitizeLookupName(validLookupName)).thenReturn(validLookupName);
        when(inputValidator.containsSqlInjectionPattern(validLookupName)).thenReturn(false);

        // Mock the service response with mutable list
        List<Map<String, Object>> mockResponse = new ArrayList<>();
        Map<String, Object> mockRow = new HashMap<>();
        mockRow.put("column1", "value1");
        mockResponse.add(mockRow);

        when(lookupMetadataService.getLookupData(anyString())).thenReturn(mockResponse);

        var result = lookupMetadataController.getLookupData(validLookupName);

        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(inputValidator).validateAndSanitizeLookupName(validLookupName);
        verify(inputValidator).containsSqlInjectionPattern(validLookupName);
    }

    @Test
    void testGetLookupData_SqlInjectionAttempt_ThrowsException() {
        String maliciousLookupName = "test' OR '1'='1";

        // Reset mock for this specific test
        reset(inputValidator);
        when(inputValidator.validateAndSanitizeLookupName(maliciousLookupName))
            .thenThrow(new IllegalArgumentException("Invalid lookup name"));

        assertThrows(IllegalArgumentException.class, () -> {
            lookupMetadataController.getLookupData(maliciousLookupName);
        });

        verify(inputValidator).validateAndSanitizeLookupName(maliciousLookupName);
    }

    @Test
    void testInputValidator_DetectsSqlInjectionPatterns() {
        // Test that the input validator properly detects various SQL injection patterns
        String[] maliciousInputs = {
            "test'; DROP TABLE users; --",
            "test' OR '1'='1",
            "test' UNION SELECT * FROM users --",
            "test'; DELETE FROM users; --",
            "test' AND 1=1 --",
            "test<script>alert('xss')</script>",
            "test\\' OR 1=1 --"
        };

        for (String maliciousInput : maliciousInputs) {
            // Reset mock for each iteration
            reset(inputValidator);
            when(inputValidator.validateAndSanitizeLookupName(maliciousInput))
                .thenThrow(new IllegalArgumentException("Invalid lookup name"));

            assertThrows(IllegalArgumentException.class, () -> {
                lookupController.addLookupData(new HashMap<>(), maliciousInput, 1L);
            }, "Should reject malicious input: " + maliciousInput);
        }
    }

    @Test
    void testInputValidator_AcceptsValidInputs() throws Exception {
        // Test that the input validator accepts valid lookup names
        String[] validInputs = {
            "test_lookup",
            "lookup1",
            "user-lookup",
            "LOOKUP_123",
            "a"
        };

        for (String validInput : validInputs) {
            // Reset mock for each iteration
            reset(inputValidator);
            when(inputValidator.validateAndSanitizeLookupName(validInput)).thenReturn(validInput);
            when(inputValidator.containsSqlInjectionPattern(validInput)).thenReturn(false);

            assertDoesNotThrow(() -> {
                lookupController.addLookupData(Map.of("col", "val"), validInput, 1L);
            }, "Should accept valid input: " + validInput);
        }
    }
}
