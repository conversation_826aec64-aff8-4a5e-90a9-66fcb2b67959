package com.emc.it.eis.gemfire.lookup.service.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.rest.core.config.RepositoryRestConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupKeyCol;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadata;

@Configuration
public class LookupConfiguration {
	
	
	@Transactional("cicadmTransactionManager")
	public  void configureRepositoryRestConfiguration(RepositoryRestConfiguration config) {
		config.exposeIdsFor(LookupMetadata.class, LookupKeyCol.class);
	}

}
