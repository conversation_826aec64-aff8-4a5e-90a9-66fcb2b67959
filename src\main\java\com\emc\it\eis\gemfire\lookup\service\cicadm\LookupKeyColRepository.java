package com.emc.it.eis.gemfire.lookup.service.cicadm;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;

@RepositoryRestResource(path = "lookupKeyCol", itemResourceRel = "lookupKeyCol")
public interface LookupKeyColRepository extends
		PagingAndSortingRepository<LookupKeyCol, Integer>,CrudRepository<LookupKeyCol, Integer> {

	LookupKeyCol findById(int id);
	
	@Query("select L from LookupKeyCol L where L.lookupName=:lookupName")
	public List<LookupKeyCol> getKeyCols(@Param("lookupName")String lookupName);

}
