package com.emc.it.eis.gemfire.lookup.service.controller;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;


import org.owasp.esapi.Encoder;
import org.owasp.esapi.codecs.OracleCodec;
import org.owasp.esapi.reference.DefaultEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadata;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadataRepository;
import com.emc.it.eis.gemfire.lookup.service.cictrans.LookupService;
import com.emc.it.eis.gemfire.lookup.service.utils.StringEscapeUtils;

import io.swagger.v3.oas.annotations.Operation;

@RestController
public class LookupController {

	@Autowired
	private LookupService lookupService;

	@Autowired
	private LookupMetadataRepository lookupMetadataRepository;

	private static Logger logger = LoggerFactory.getLogger(LookupController.class);

	@Operation(description = "lookupData", summary = "Lookup Data")
	@PostMapping("/lookupData/{lookupName}")
	@Transactional("cictransTransactionManager")
	public HttpStatus addLookupData(@RequestBody Map<String, Object> lookupData,
			@PathVariable String lookupName, @RequestParam long userId)
			throws IOException, ClassNotFoundException, SQLException {
		Encoder esapiEncoder = DefaultEncoder.getInstance();
		OracleCodec oracleCodec = new OracleCodec();
		String encodedLookupName = esapiEncoder.encodeForSQL(oracleCodec, lookupName);
		String sanitizedLookupName =StringEscapeUtils.escapeSql(encodedLookupName);
		logger.info("Inserting lookup data for lookup  name: {}", lookupName);
		LookupMetadata lookupMetadata = lookupMetadataRepository.findByLookupName(sanitizedLookupName);
		Map<String, Object> sanitizedlookupData = sanitizeRequestBodyInput(lookupData);
		sanitizedlookupData.remove("lookupName");
		sanitizedlookupData.remove("r");

		sanitizedlookupData = lookupService.getColumnsCheck(lookupMetadata.getTableName(), sanitizedlookupData, userId);
		HttpStatus status = lookupService.insertLookupData(lookupMetadata.getTableName(), sanitizedlookupData);
		logger.debug(" Lookup data inserted  status of the operation{}", status);
		return status;
	}

	@Operation(description = "lookupData", summary = "Lookup Data")
	@PutMapping("/lookupData/{lookupName}")
	@Transactional("cictransTransactionManager")
	public HttpStatus updateLookupData(@RequestBody Map<String, Object> lookupData,
			@PathVariable String lookupName, @RequestParam long userId)
			throws IOException, ClassNotFoundException, SQLException {
		Encoder esapiEncoder = DefaultEncoder.getInstance();
		OracleCodec oracleCodec = new OracleCodec();
		logger.info("Inserting lookup data for lookup name: {}", lookupName);
		String encodedLookupName = esapiEncoder.encodeForSQL(oracleCodec, lookupName);
		String sanitizedLookupName = StringEscapeUtils.escapeSql(encodedLookupName);
		LookupMetadata lookupMetadata = lookupMetadataRepository.findByLookupName(sanitizedLookupName);
		Map<String, Object> sanitizedlookupData = sanitizeRequestBodyInput(lookupData);
		sanitizedlookupData.remove("lookupName");
		sanitizedlookupData.remove("r");
		sanitizedlookupData = lookupService.getColumnsCheck(lookupMetadata.getTableName(), sanitizedlookupData, userId);
		Map<String, Object> newLookupData = new HashMap<>();
		Map<String, Object> existingLookupData = new HashMap<>();
		for (Map.Entry<String, Object> entry : sanitizedlookupData.entrySet()) {
			if (entry.getKey().contains("old_")) {
				String newKey = entry.getKey().replace("old_", "");
				existingLookupData.put(newKey, sanitizedlookupData.get(entry.getKey()));
			} else {
				newLookupData.put(entry.getKey(), sanitizedlookupData.get(entry.getKey()));
			}
		}
		HttpStatus status = lookupService.updateLookupData(lookupMetadata.getTableName(), newLookupData,
				existingLookupData);
		logger.debug(" Lookup data inserted  status of the operation{}", status);
		return status;
	}

	@Operation(description = "lookupData", summary = "Lookup Data")
	@DeleteMapping("/lookupData/{lookupName}")
	@Transactional("cictransTransactionManager")
	public HttpStatus deleteLookupData(@RequestBody Map<String, Object> lookupData,
			@PathVariable String lookupName)
	
			throws IOException, ClassNotFoundException, SQLException {
		Encoder esapiEncoder = DefaultEncoder.getInstance();
		OracleCodec oracleCodec = new OracleCodec();
		logger.info("Deleting lookup data for lookup name: {}", lookupName);
		String encodedLookupName = esapiEncoder.encodeForSQL(oracleCodec, lookupName);
		String sanitizedLookupName = StringEscapeUtils.escapeSql(encodedLookupName);
		LookupMetadata lookupMetadata = lookupMetadataRepository.findByLookupName(sanitizedLookupName);
		Map<String, Object> sanitizedlookupData = sanitizeRequestBodyInput(lookupData);
		HttpStatus status = lookupService.deleteLookupData(lookupMetadata.getTableName(), sanitizedlookupData);
		logger.debug(" Lookup data deleted  status of the operation{}", status);
		return status;
	}
	
	
	private Map<String, Object> sanitizeRequestBodyInput(Map<String, Object> inputRequestBody) {
        Map<String, Object> sanitizedRequestBody = new HashMap<>();
        for (Map.Entry<String, Object> entry : inputRequestBody.entrySet()) {
            Object value = entry.getValue();
            sanitizedRequestBody.put(StringEscapeUtils.escapeSql(entry.getKey()), StringEscapeUtils.escapeSql(value.toString()));
        }
        return sanitizedRequestBody;
    }

}
