package com.emc.it.eis.gemfire.lookup.service.config;

import java.util.HashMap;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@EnableJpaRepositories(basePackages = "com.emc.it.eis.gemfire.lookup.service.cictrans", entityManagerFactoryRef = "cictransEntityManager", transactionManagerRef = "cictransTransactionManager")
public class CictransConfiguration {

	@Autowired
	@Qualifier(value = "cictrans.dataSource")
	DataSource cictransDataSource;

	private String hibernateDialect = "org.hibernate.dialect.OracleDialect";

	private String hbm2ddlValue = "validate";

	@Primary
	@Bean
	public LocalContainerEntityManagerFactoryBean cictransEntityManager() {
		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(cictransDataSource);
		em.setPackagesToScan(new String[]{"com.emc.it.eis.gemfire.lookup.service.cictrans"});

		HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		HashMap<String, Object> properties = new HashMap<>();
		properties.put("hibernate.hbm2ddl.validate", hbm2ddlValue);
		properties.put("hibernate.dialect", hibernateDialect);
		em.setJpaPropertyMap(properties);

		return em;
	}

	@Primary
	@Bean
	public PlatformTransactionManager cictransTransactionManager() {
		JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(cictransEntityManager().getObject());
		return transactionManager;
	}

	@Primary
	@Bean
	public JdbcTemplate cictransJdbcTemplate() {
		JdbcTemplate jdbcTemplate = new JdbcTemplate();
		jdbcTemplate.setDataSource(cictransDataSource);
		return jdbcTemplate;
	}
}
