package com.emc.it.eis.gemfire.lookup.service.security;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Unit tests for InputValidator to verify SQL injection prevention
 */
public class InputValidatorTest {

    private InputValidator inputValidator;

    @BeforeEach
    void setUp() {
        inputValidator = new InputValidator();
    }

    @Test
    void testValidateAndSanitizeLookupName_ValidInputs_Success() {
        // Test valid lookup names
        String[] validInputs = {
            "test_lookup",
            "lookup1",
            "user-lookup",
            "LOOKUP_123",
            "a",
            "valid_name_123"
        };

        for (String validInput : validInputs) {
            assertDoesNotThrow(() -> {
                String result = inputValidator.validateAndSanitizeLookupName(validInput);
                assertEquals(validInput, result);
            }, "Should accept valid input: " + validInput);
        }
    }

    @Test
    void testValidateAndSanitizeLookupName_SqlInjectionAttempts_ThrowsException() {
        // Test malicious inputs that should be rejected
        String[] maliciousInputs = {
            "test'; DROP TABLE users; --",
            "test' OR '1'='1",
            "test' UNION SELECT * FROM users --",
            "test'; DELETE FROM users; --",
            "test' AND 1=1 --",
            "test\"",
            "test\\",
            "test%",
            "test*",
            "test<script>",
            "test>alert",
            "test UNION",
            "test SELECT",
            "test INSERT",
            "test UPDATE",
            "test DELETE",
            "test DROP",
            "test CREATE",
            "test ALTER",
            "test EXEC",
            "test EXECUTE"
        };

        for (String maliciousInput : maliciousInputs) {
            assertThrows(IllegalArgumentException.class, () -> {
                inputValidator.validateAndSanitizeLookupName(maliciousInput);
            }, "Should reject malicious input: " + maliciousInput);
        }
    }

    @Test
    void testValidateAndSanitizeLookupName_NullAndEmpty_ThrowsException() {
        // Test null input
        assertThrows(IllegalArgumentException.class, () -> {
            inputValidator.validateAndSanitizeLookupName(null);
        });

        // Test empty input
        assertThrows(IllegalArgumentException.class, () -> {
            inputValidator.validateAndSanitizeLookupName("");
        });

        // Test whitespace only
        assertThrows(IllegalArgumentException.class, () -> {
            inputValidator.validateAndSanitizeLookupName("   ");
        });
    }

    @Test
    void testValidateAndSanitizeLookupName_TooLong_ThrowsException() {
        // Test input that's too long (over 50 characters)
        String tooLongInput = "a".repeat(51);
        
        assertThrows(IllegalArgumentException.class, () -> {
            inputValidator.validateAndSanitizeLookupName(tooLongInput);
        });
    }

    @Test
    void testContainsSqlInjectionPattern_DetectsPatterns() {
        // Test that SQL injection patterns are detected
        String[] sqlInjectionPatterns = {
            "test'; DROP TABLE users; --",
            "test' OR '1'='1",
            "test\" OR \"1\"=\"1",
            "test' UNION SELECT",
            "test-- comment",
            "test<script>",
            "test\\escape",
            "test%wildcard",
            "test*wildcard"
        };

        for (String pattern : sqlInjectionPatterns) {
            assertTrue(inputValidator.containsSqlInjectionPattern(pattern),
                "Should detect SQL injection pattern: " + pattern);
        }
    }

    @Test
    void testContainsSqlInjectionPattern_AcceptsValidInputs() {
        // Test that valid inputs are not flagged as SQL injection
        String[] validInputs = {
            "test_lookup",
            "lookup1",
            "user-lookup",
            "LOOKUP_123",
            "valid_name"
        };

        for (String validInput : validInputs) {
            assertFalse(inputValidator.containsSqlInjectionPattern(validInput),
                "Should not detect SQL injection in valid input: " + validInput);
        }
    }

    @Test
    void testIsValidTableName() {
        // Test valid table names
        assertTrue(inputValidator.isValidTableName("valid_table"));
        assertTrue(inputValidator.isValidTableName("schema.table"));
        assertTrue(inputValidator.isValidTableName("_underscore_table"));
        assertTrue(inputValidator.isValidTableName("TABLE123"));

        // Test invalid table names
        assertFalse(inputValidator.isValidTableName("123invalid"));
        assertFalse(inputValidator.isValidTableName("table-name"));
        assertFalse(inputValidator.isValidTableName("table name"));
        assertFalse(inputValidator.isValidTableName("table'; DROP"));
        assertFalse(inputValidator.isValidTableName(null));
        assertFalse(inputValidator.isValidTableName(""));
    }

    @Test
    void testIsValidColumnName() {
        // Test valid column names
        assertTrue(inputValidator.isValidColumnName("valid_column"));
        assertTrue(inputValidator.isValidColumnName("_underscore_column"));
        assertTrue(inputValidator.isValidColumnName("COLUMN123"));
        assertTrue(inputValidator.isValidColumnName("column_name_123"));

        // Test invalid column names
        assertFalse(inputValidator.isValidColumnName("123invalid"));
        assertFalse(inputValidator.isValidColumnName("column-name"));
        assertFalse(inputValidator.isValidColumnName("column name"));
        assertFalse(inputValidator.isValidColumnName("column'; DROP"));
        assertFalse(inputValidator.isValidColumnName("column.name"));
        assertFalse(inputValidator.isValidColumnName(null));
        assertFalse(inputValidator.isValidColumnName(""));
    }

    @Test
    void testSanitizeStringInput() {
        // Test that dangerous characters are removed
        assertEquals("test", inputValidator.sanitizeStringInput("test';"));
        assertEquals("test", inputValidator.sanitizeStringInput("test\""));
        assertEquals("test", inputValidator.sanitizeStringInput("test--"));
        assertEquals("test", inputValidator.sanitizeStringInput("test<>"));
        assertEquals("test", inputValidator.sanitizeStringInput("test\\"));
        
        // Test null input
        assertNull(inputValidator.sanitizeStringInput(null));
        
        // Test normal input is preserved
        assertEquals("normal_input_123", inputValidator.sanitizeStringInput("normal_input_123"));
    }

    @Test
    void testWhitespaceHandling() {
        // Test that leading/trailing whitespace is handled
        assertEquals("test", inputValidator.validateAndSanitizeLookupName("  test  "));
        assertEquals("test_lookup", inputValidator.validateAndSanitizeLookupName("\ttest_lookup\n"));
    }
}
