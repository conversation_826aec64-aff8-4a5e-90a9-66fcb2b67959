package com.emc.it.eis.gemfire.lookup.service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, RabbitAutoConfiguration.class})
@ComponentScan(basePackages = {"com.emc.it.eis.gemfire.lookup.service", "com.emc.it.eis.oauth2server"})
@ImportResource("classpath:application-context.xml")
public class Application
{
   public static void main(String[] args)
   {
      SpringApplication.run(Application.class, args);
   }
}
