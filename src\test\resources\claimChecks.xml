<?xml version="1.0" encoding="UTF-8"?>
<svc:DistributeCaseRequest xmlns:msg="http://emc.com/it/enterprise/msg/v1" xmlns:data="http://emc.com/it/enterprise/data/v1" xmlns:svc="http://emc.com/it/enterprise/contract/distributeCase/v1">
<data:PayloadContext>
<msg:ApplicationProfile>
<msg:AppName>SFDC</msg:AppName>
</msg:ApplicationProfile>
</data:PayloadContext>
<data:Document>
<data:CaseDetails>
<data:CaseNumber>77786102</data:CaseNumber>
<data:CaseTitle>Peacock Test 2</data:CaseTitle>
<data:CaseCategory>Software</data:CaseCategory>
<data:Status>Working</data:Status>
<data:Severity>S3</data:Severity>
<data:ProblemDescription>Peacock Test 2</data:ProblemDescription>
<data:OwnerName>Lotfy, <PERSON>tham</data:OwnerName>
<data:OwnerId>005j000000BdcoYAAR</data:OwnerId>
<data:GroupName>Service Cloud</data:GroupName>
<data:ContactInfo>
<data:MUP_User_Preferred_Language__c>German</data:MUP_User_Preferred_Language__c>
</data:ContactInfo>
<data:ProductDetails>
<data:ProductName>Captiva Dispatcher</data:ProductName>
<data:Version>6.5 SP1</data:Version>
<data:ProductFamily>Captiva</data:ProductFamily>
</data:ProductDetails>
<data:SourceName>ServiceCloud</data:SourceName>
<data:SourceReference>50021000000iB5fAAE</data:SourceReference>
<data:TargetReference>SR-30726</data:TargetReference>
<data:Channel>Phone</data:Channel>
<data:NextActionDate>2016-04-27T08:22:00.000Z</data:NextActionDate>
<data:CreateDate>2016-04-19T20:30:28.000Z</data:CreateDate>
<data:ObjectVersion>2</data:ObjectVersion>
<data:ActualProblem>Please update the Actual Problem field below, capturing the summary of the customer's issue. This field must be updated prior to the SR being Closed.</data:ActualProblem>
<data:ReOpen>false</data:ReOpen>
<data:ServiceRequestAge>14 Days, 9 Hours, 11 Minutes</data:ServiceRequestAge>
<data:TimeincurrentStatus>14 Days, 5 Hours, 11 Minutes</data:TimeincurrentStatus>
<data:PegaTurnOverTime>2016-04-27T06:22:00.000Z</data:PegaTurnOverTime>
<data:BadgeId>111725</data:BadgeId>
<data:AccountDetails>
<data:AccountId>00121000004f4VDAAY</data:AccountId>
<data:IMAPSiteID>********</data:IMAPSiteID>
<data:BillingCountry>United States</data:BillingCountry>
<data:Name>XYZ</data:Name>
</data:AccountDetails>
<data:Origin>Phone</data:Origin>
<data:EntitlementBypass>false</data:EntitlementBypass>
<data:CurrentImpact>false</data:CurrentImpact>
<data:SRHealthStage>Normal</data:SRHealthStage>
<data:OracleGroupId>*********</data:OracleGroupId>
</data:CaseDetails>
</data:Document>
</svc:DistributeCaseRequest>
