package com.emc.it.eis.gemfire.lookup.service.cicadm;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;

@RepositoryRestResource(path = "lookupMetadata", itemResourceRel = "/lookupMetadata")
public interface LookupMetadataRepository extends
		PagingAndSortingRepository<LookupMetadata, String>,CrudRepository<LookupMetadata, String> {

	LookupMetadata findByLookupName(String lookupName);

	@Query("select L from LookupMetadata L WHERE L.lookupName LIKE :name")
	public Iterable<LookupMetadata> searchLookupMetadata(@Param("name") String name);

	
}
