<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xsi:schemaLocation="http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-4.1.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
		
		
	<import	resource="classpath:META-INF/spring/cic-config/common-datasource-config.xml" />


	<beans profile="local, standalone, test,kob">
		<bean id="cicadm.dataSource" name="cicadm.dataSource"
			class=" org.springframework.jdbc.datasource.DriverManagerDataSource"
			autowire="byName">
			<property name="username" value="${cicadm.db.username}" />
			<property name="password" value="${cicadm.db.password}" />
			<property name="driverClassName" value="${jdbc.driverClassName}" />
			<property name="url" value="${cicadm.db.url}" />
		</bean>
		<bean id="cictrans.dataSource" name="cictrans.dataSource"
			class=" org.springframework.jdbc.datasource.DriverManagerDataSource"
			autowire="byName" primary="true">
			<property name="username" value="${cictrans.db.username}" />
			<property name="password" value="${cictrans.db.password}" />
			<property name="driverClassName" value="${jdbc.driverClassName}" />
			<property name="url" value="${cictrans.db.url}" />
		</bean>
		<bean id="gemfire.dataSource" name="gemfire.dataSource"
			class=" org.springframework.jdbc.datasource.DriverManagerDataSource"
			autowire="byName">
			<property name="username" value="${gemfire.db.username}" />
			<property name="password" value="${gemfire.db.password}" />
			<property name="driverClassName" value="${jdbc.driverClassName}" />
			<property name="url" value="${gemfire.db.url}" />
		</bean>
	</beans>

	<beans profile="tomcat">
		<jee:jndi-lookup jndi-name="jdbc/intdb" id="cicadm.dataSource"/>
		<jee:jndi-lookup jndi-name="jdbc/cictrans" id="cictrans.dataSource"/>
		<jee:jndi-lookup jndi-name="jdbc/gemfire" id="gemfire.dataSource"/>
	</beans>

	<beans profile="cloud">
		<bean id="cicadmHikariConfig" parent="abstractOracleHikariConfig">
			<property name="poolName" value="cicadmHikariCP" />
			<property name="dataSourceProperties">
		        <props>
		            <prop key="url">#{cicadmUrl}</prop>            
		        </props>
		    </property>	
		</bean>
		
		<bean id="cicadmUrl" class="com.emc.it.eis.common.cloud.service.ServiceFactoryBean">
			<constructor-arg value="aicshd_postgres_cicadm"></constructor-arg>
		</bean>
		
		<bean id="cicadm.dataSource" parent="abstractOracleDS" autowire="byName">
		    <constructor-arg ref="cicadmHikariConfig" />
		</bean>
		
		<bean id="cictransHikariConfig" parent="abstractOracleHikariConfig">
			<property name="poolName" value="cictransHikariCP" />			
			<property name="dataSourceProperties">
		        <props>
		            <prop key="url">#{cictransUrl}</prop>            
		        </props>
		    </property>		
		</bean>
		
		<bean id="cictransUrl" class="com.emc.it.eis.common.cloud.service.ServiceFactoryBean">
			<constructor-arg value="aicshd_postgres_cictrans"></constructor-arg>
		</bean>
		
		<bean id="cictrans.dataSource" parent="abstractOracleDS" autowire="byName" primary="true">
		    <constructor-arg ref="cictransHikariConfig" />
		</bean>
		
		<bean id="gemfireHikariConfig" parent="abstractOracleHikariConfig">
			<property name="poolName" value="gemfireHikariCP" />			
			<property name="dataSourceProperties">
		        <props>
		            <prop key="url">#{gemfireUrl}</prop>            
		        </props>
		    </property>			
		</bean>
		
		<bean id="gemfireUrl" class="com.emc.it.eis.common.cloud.service.ServiceFactoryBean">
			<constructor-arg value="aicshd_postgres_gemfire"></constructor-arg>
		</bean>
		
		<bean id="gemfire.dataSource" parent="abstractOracleDS" autowire="byName">
		    <constructor-arg ref="gemfireHikariConfig" />
		</bean>
	</beans>

</beans>
