/*
 * Copyright 2016 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.emc.it.eis.gemfire.lookup.service.test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer.MethodName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.util.ReflectionTestUtils;

import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupKeyCol;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupKeyColRepository;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadata;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadataRepository;
import com.emc.it.eis.gemfire.lookup.service.cicadm.LookupMetadataService;
import com.emc.it.eis.gemfire.lookup.service.controller.LookupMetadataController;
import com.emc.it.eis.gemfire.lookup.service.security.InputValidator;

@ExtendWith(SpringExtension.class)
@WebAppConfiguration
@PropertySource("classpath:db-test.properties")
@ActiveProfiles({"test"})
@TestMethodOrder(MethodName.class)
class LookupMetadataControllerTests {

	LookupMetadataController lookupMetadataController;

	private LookupMetadataRepository lookupMetadataRepository;

	private LookupKeyColRepository lookupKeyColRepository;

	private LookupMetadataService lookupMetadataService;

	private LookupMetadata lookupMetadata;

	private LookupKeyCol lookupKeyCol;

	private List<LookupKeyCol> lookupKeyColList;

	private JdbcTemplate jdbcTemplate;

	private Map<String, Object> insertMap;

	private InputValidator inputValidator;

	@BeforeEach
	void setUp() throws IOException {

		lookupMetadataController = new LookupMetadataController();
		lookupMetadataService = new LookupMetadataService();

		lookupMetadataRepository = Mockito.mock(LookupMetadataRepository.class);

		lookupKeyColRepository = Mockito.mock(LookupKeyColRepository.class);
		jdbcTemplate = Mockito.mock(JdbcTemplate.class);
		inputValidator = Mockito.mock(InputValidator.class);

		// Mock InputValidator to return valid inputs for test cases
		when(inputValidator.validateAndSanitizeLookupName(Mockito.anyString())).thenAnswer(invocation -> invocation.getArgument(0));
		when(inputValidator.containsSqlInjectionPattern(Mockito.anyString())).thenReturn(false);

		ReflectionTestUtils.setField(lookupMetadataService, "cicadmJdbcTemplate", jdbcTemplate);
		ReflectionTestUtils.setField(lookupMetadataService, "lookupMetadataRepository", lookupMetadataRepository);
		ReflectionTestUtils.setField(lookupMetadataService, "lookupKeyColRepository", lookupKeyColRepository);
		ReflectionTestUtils.setField(lookupMetadataController, "lookupMetadataRepository", lookupMetadataRepository);
		ReflectionTestUtils.setField(lookupMetadataController, "lookupMetadataService", lookupMetadataService);
		ReflectionTestUtils.setField(lookupMetadataController, "inputValidator", inputValidator);

		lookupMetadata = new LookupMetadata();
		lookupMetadata.setLookupName("Lookup1");
		lookupMetadata.setSql("select * from Lookup1");
		lookupMetadata.setTableName("Table1");

		lookupKeyCol = new LookupKeyCol();
		lookupKeyCol.setColumnAlias("key1");
		lookupKeyCol.setLookupName("Lookup1");
		lookupKeyCol.setId(12);

		lookupKeyColList = new ArrayList<LookupKeyCol>();
		lookupKeyColList.add(lookupKeyCol);

		insertMap = new HashMap<>();
		insertMap.put("lookupName", "value1");
		insertMap.put("tableName", "value1");
		insertMap.put("sql", "value1");
		insertMap.put("lookupKeysColumns", "key1, key2");
		insertMap.put("r", 26);

	}

	@Test
	void getLookupData() throws Exception {
		List<Map<String, Object>> listMap = new ArrayList<>();
		Map<String, Object> map = new HashMap<>();
		 Map<String, Object> columns = new HashMap<>();

		listMap.add(map);
		columns.put("columnName", "abc");
		

		
		when(lookupMetadataRepository.findByLookupName(Mockito.anyString())).thenReturn(lookupMetadata);
		//when(jdbcTemplate.queryForList(Mockito.anyString())).thenReturn(listMap);
		
		
		when(jdbcTemplate.query(Mockito.anyString(), Mockito.any(ResultSetExtractor.class)))
        .thenReturn(map);
		
		ResultSet rs = Mockito.mock(ResultSet.class);
	    Mockito.when(rs.next()).thenReturn(true).thenReturn(false);
	    Mockito.when(rs.getString(1)).thenReturn("Value 1");
	    Mockito.when(rs.getString(2)).thenReturn("Value 2");
	
	  
		Mockito.doNothing().when(lookupMetadataRepository).delete(Mockito.any(LookupMetadata.class));
		List<Map<String, Object>> listMapResult = lookupMetadataController.getLookupData("lookup1");
		assertNotNull(listMapResult);
		
		
		 //ResultSetMetaData metadata = rs.getMetaData();
		 //Assert.assertEquals(metadata.getColumnCount(), 2);
		

        // prepare main mock for result set and define when 
        // main mock to return dependant mock
        //ResultSet rs = Mockito.mock(ResultSet.class);
        //when(rs.getMetaData()).thenReturn(rsMetaMock);

        // application logic
        //ResultSetMetaData rsmd = rs.getMetaData();

        //assertions
      //  Assert.assertEquals(rsmd.getColumnName(1), "lookup1");
		

	}

	@Test
	void getLookups() throws Exception {
		List<LookupMetadata> metadataList = new ArrayList<LookupMetadata>();
		metadataList.add(lookupMetadata);
		List<Map<String, Object>> listMap = new ArrayList<>();
		Map<String, Object> map = new HashMap<>();
		
		listMap.add(map);
	
		when(lookupMetadataRepository.findAll()).thenReturn(metadataList);
		when(lookupMetadataRepository.searchLookupMetadata(Mockito.anyString())).thenReturn(metadataList);
		when(lookupKeyColRepository.getKeyCols(Mockito.anyString())).thenReturn(lookupKeyColList);
		when(jdbcTemplate.queryForList(Mockito.anyString())).thenReturn(listMap);
		
		//when(jdbcTemplate.queryForList(Mockito.anyString())).thenReturn(listMap);
		
		
		
		List<Map<String, Object>> listMapResult = lookupMetadataController.getLookups(null);
		assertNotNull(listMapResult);
		listMapResult = lookupMetadataController.getLookups("L*");
		assertNotNull(listMapResult);
	}

	@Test
	void addLookup() throws Exception {
		List<LookupMetadata> metadataList = new ArrayList<LookupMetadata>();
		metadataList.add(lookupMetadata);
		List<Map<String, Object>> listMap = new ArrayList<>();
		when(lookupKeyColRepository.getKeyCols(Mockito.anyString())).thenReturn(lookupKeyColList);
		when(lookupKeyColRepository.save(Mockito.any(LookupKeyCol.class))).thenReturn(lookupKeyCol);
		doNothing().when(lookupKeyColRepository).delete(Mockito.any(LookupKeyCol.class));
		when(lookupMetadataRepository.save(Mockito.any(LookupMetadata.class))).thenReturn(lookupMetadata);
		when(jdbcTemplate.queryForList(Mockito.anyString())).thenReturn(listMap);
		HttpStatus httpStatus = lookupMetadataController.addLookup(insertMap);
		assertNotNull(httpStatus);
		assertEquals(HttpStatus.CREATED, httpStatus);
	}

	@Test
	void deleteLookup() throws Exception {
		when(lookupKeyColRepository.getKeyCols(Mockito.anyString())).thenReturn(lookupKeyColList);
		doNothing().when(lookupKeyColRepository).delete(Mockito.any(LookupKeyCol.class));
		doNothing().when(lookupMetadataRepository).delete(Mockito.any(LookupMetadata.class));
		HttpStatus httpStatus = lookupMetadataController.deleteLookup("Lookup1");
		assertNotNull(httpStatus);
		assertEquals(HttpStatus.ACCEPTED, httpStatus);
	}

}
