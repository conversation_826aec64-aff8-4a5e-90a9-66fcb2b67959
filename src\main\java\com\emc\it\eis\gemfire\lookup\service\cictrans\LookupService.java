package com.emc.it.eis.gemfire.lookup.service.cictrans;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.Encoder;
import org.owasp.esapi.codecs.OracleCodec;
import org.owasp.esapi.reference.DefaultEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class LookupService {

	@Autowired
	JdbcTemplate cictransJdbcTemplate;
	private static Logger logger = LoggerFactory.getLogger(LookupService.class);
	private static String andStr = " and ";

	public Map<String, Object> getColumnsCheck(String tableName, Map<String, Object> lookupData, long userId) {
		try (Connection connection = cictransJdbcTemplate.getDataSource().getConnection()) {
			DatabaseMetaData metadata = connection.getMetaData();

			String[] tableArray = tableName.split("\\.");
			ResultSet resultSet = null;
			if (tableArray.length > 1) {
				resultSet = metadata.getColumns(null, null, StringUtils.lowerCase(tableArray[1]), null);
			} else {
				resultSet = metadata.getColumns(null, null, StringUtils.lowerCase(tableArray[0]), null);
			}
			while (resultSet.next()) {
				String name = resultSet.getString("COLUMN_NAME");
				if ("user_id".equals(name)) {
					lookupData.put("user_id", userId);
				}
				if ("last_modified_date".equals(name)) {
					Date utilDate = new Date();
					Timestamp sqlDate = new Timestamp(utilDate.getTime());
					lookupData.put("last_modified_date", sqlDate);
				}
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}

		return lookupData;
	}

	@Transactional("cictransTransactionManager")
	public HttpStatus insertLookupData(String tableName, Map<String, Object> lookupData) throws SQLException {
		logger.info("In insertLookupData: tableName {}", tableName);
		StringBuilder insertQuery;
		List<String> columns = new ArrayList<>();
		List<Object> values = new ArrayList<>();
		Encoder esapiEncoder = DefaultEncoder.getInstance();
		for (Map.Entry<String, Object> entry : lookupData.entrySet()) {
			columns.add(esapiEncoder.encodeForSQL(new OracleCodec(), entry.getKey()));
			values.add(esapiEncoder.encodeForSQL(new OracleCodec(), entry.getValue().toString()));	
		}
		insertQuery = new StringBuilder("insert into ");
		insertQuery.append(tableName).append(" (");
		for (String key : columns) {
			insertQuery.append(key).append(", ");
		}
		insertQuery.deleteCharAt(insertQuery.lastIndexOf(","));
		insertQuery.append(" ) values ( ");
		for (Object data : values) {
			if (data instanceof String || data instanceof Timestamp) {
				insertQuery.append("'").append(data).append("'").append(",");
			} else {
				insertQuery.append(data).append(", ");
			}
		}
		insertQuery.deleteCharAt(insertQuery.lastIndexOf(","));
		insertQuery.append(" ) ");

		
		//String sanitizedInsertQuery = esapiEncoder.encodeForSQL(new OracleCodec(), insertQuery.toString());

		//LOGGER.info("Insert statement executing: " + sanitizedInsertQuery.toString());

		cictransJdbcTemplate.execute(insertQuery.toString());

		return HttpStatus.CREATED;
	}

	public HttpStatus deleteLookupData(String lookupTableName, Map<String, Object> lookupData) throws SQLException {
		logger.info("In deleteLookupData: tableName {}", lookupTableName);
		StringBuilder deleteQuery;
		deleteQuery = new StringBuilder("delete from ");
		deleteQuery.append(lookupTableName).append(" where ");
		//String sanitizedInsertQuery = null;
		Encoder esapiEncoder = DefaultEncoder.getInstance();
		OracleCodec oracleCodec = new OracleCodec();
		for (Map.Entry<String, Object> entry : lookupData.entrySet()) {
			String key = esapiEncoder.encodeForSQL(oracleCodec, entry.getKey());
			Object data = esapiEncoder.encodeForSQL(oracleCodec, entry.getValue().toString());
			//deleteQuery.append(entry.getKey()).append(" = ");
			deleteQuery.append(key).append(" = ");
			//Object data = esapiEncoder.encodeForSQL(oracleCodec,lookupData.get(entry.getKey()).toString());

			if (data instanceof String || data instanceof Timestamp) {
				deleteQuery.append("'").append(data).append("'").append(andStr);
				
				// sanitizedInsertQuery = esapiEncoder.encodeForSQL(oracleCodec, deleteQuery.toString());
			} else {
				deleteQuery.append(data).append(andStr);
				
				// sanitizedInsertQuery = esapiEncoder.encodeForSQL(oracleCodec, deleteQuery.toString());
			}
		}
		int index = deleteQuery.toString().lastIndexOf("and");
		String deleteStr = deleteQuery.substring(0, index);
		logger.info("Delete statement executing: {}", deleteStr);

		cictransJdbcTemplate.execute(deleteStr);

		return HttpStatus.ACCEPTED;
	}

	public HttpStatus updateLookupData(String lookupTableName, Map<String, Object> newLookupData,
			Map<String, Object> existingLookupData) throws SQLException {
		logger.info("In updateLookupData: tableName {}", lookupTableName);
		StringBuilder updateQuery = new StringBuilder("update  ");
		updateQuery.append(lookupTableName).append(" set ");
		Encoder esapiEncoder = DefaultEncoder.getInstance();
		OracleCodec oracleCodec = new OracleCodec();

		for (Map.Entry<String, Object> entry : newLookupData.entrySet()) {
			String key = esapiEncoder.encodeForSQL(oracleCodec, entry.getKey());
			Object data = esapiEncoder.encodeForSQL(oracleCodec, entry.getValue().toString());
			updateQuery.append(key).append(" = ");
			if (data instanceof String || data instanceof Timestamp) {
				updateQuery.append("'").append(data).append("'").append(" , ");
			} else {
				updateQuery.append(data).append(" , ");
			}
		}
		updateQuery.deleteCharAt(updateQuery.lastIndexOf(","));
		updateQuery.append(" where ");

		for (Map.Entry<String, Object> entry : existingLookupData.entrySet()) {
			String key = esapiEncoder.encodeForSQL(oracleCodec, entry.getKey());
			Object data = esapiEncoder.encodeForSQL(oracleCodec, existingLookupData.get(key).toString());

			updateQuery.append(key).append(" = ");
			if (data instanceof String || data instanceof Timestamp) {
				updateQuery.append("'").append(data).append("'").append(andStr);
			} else {
				updateQuery.append(data).append(andStr);
			}
		}
		int index = updateQuery.toString().lastIndexOf("and");
		String updateStr = updateQuery.substring(0, index);
		
		//String sanitizedUpdateQuery = esapiEncoder.encodeForSQL(new OracleCodec(), updateStr.toString());

		//LOGGER.info("Update statement executing: " + sanitizedUpdateQuery);

		cictransJdbcTemplate.execute(updateStr);

		return HttpStatus.CREATED;
	}
}
