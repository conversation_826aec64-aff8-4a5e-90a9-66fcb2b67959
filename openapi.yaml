openapi: 3.0.1
info:
  title: Gemfire Lookup Service
  description: Api for Gemfire Lookup Service
  license:
    name: Apache License Version 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0
  version: '1.0'
servers:
  - url: https://gemfire-lookup-service-oauth2.qas.aic.us.dell.com
    description: Generated server url
paths:
  /cicClaimcheckses:
    get:
      tags:
        - cic-claimchecks-entity-controller
      description: get-cicclaimchecks
      operationId: getCollectionResource-cicclaimchecks-get_1
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/CollectionModelEntityModelCicClaimchecks'
            application/x-spring-data-compact+json:
              schema:
                $ref: '#/components/schemas/CollectionModelEntityModelCicClaimchecks'
            text/uri-list:
              schema:
                type: string
    post:
      tags:
        - cic-claimchecks-entity-controller
      description: create-cicclaimchecks
      operationId: postCollectionResource-cicclaimchecks-post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CicClaimchecksRequestBody'
        required: true
      responses:
        '201':
          description: Created
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelCicClaimchecks'
  /cicClaimcheckses/{id}:
    get:
      tags:
        - cic-claimchecks-entity-controller
      description: get-cicclaimchecks
      operationId: getItemResource-cicclaimchecks-get
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelCicClaimchecks'
        '404':
          description: Not Found
    put:
      tags:
        - cic-claimchecks-entity-controller
      description: update-cicclaimchecks
      operationId: putItemResource-cicclaimchecks-put
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CicClaimchecksRequestBody'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelCicClaimchecks'
        '201':
          description: Created
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelCicClaimchecks'
        '204':
          description: No Content
    delete:
      tags:
        - cic-claimchecks-entity-controller
      description: delete-cicclaimchecks
      operationId: deleteItemResource-cicclaimchecks-delete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No Content
        '404':
          description: Not Found
    patch:
      tags:
        - cic-claimchecks-entity-controller
      description: patch-cicclaimchecks
      operationId: patchItemResource-cicclaimchecks-patch
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CicClaimchecksRequestBody'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelCicClaimchecks'
        '204':
          description: No Content
  /lookupKeyCol:
    get:
      tags:
        - lookup-key-col-entity-controller
      description: get-lookupkeycol
      operationId: getCollectionResource-lookupkeycol-get_1
      parameters:
        - name: page
          in: query
          description: Zero-based page index (0..N)
          required: false
          schema:
            minimum: 0
            type: integer
            default: 0
        - name: size
          in: query
          description: The size of the page to be returned
          required: false
          schema:
            minimum: 1
            type: integer
            default: 20
        - name: sort
          in: query
          description: 'Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.'
          required: false
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/PagedModelEntityModelLookupKeyCol'
            application/x-spring-data-compact+json:
              schema:
                $ref: '#/components/schemas/PagedModelEntityModelLookupKeyCol'
            text/uri-list:
              schema:
                type: string
    post:
      tags:
        - lookup-key-col-entity-controller
      description: create-lookupkeycol
      operationId: postCollectionResource-lookupkeycol-post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LookupKeyColRequestBody'
        required: true
      responses:
        '201':
          description: Created
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupKeyCol'
  /lookupKeyCol/search/getKeyCols:
    get:
      tags:
        - lookup-key-col-search-controller
      operationId: executeSearch-lookupkeycol-get
      parameters:
        - name: lookupName
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/CollectionModelEntityModelLookupKeyCol'
        '404':
          description: Not Found
  /lookupKeyCol/{id}:
    get:
      tags:
        - lookup-key-col-entity-controller
      description: get-lookupkeycol
      operationId: getItemResource-lookupkeycol-get
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupKeyCol'
        '404':
          description: Not Found
    put:
      tags:
        - lookup-key-col-entity-controller
      description: update-lookupkeycol
      operationId: putItemResource-lookupkeycol-put
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LookupKeyColRequestBody'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupKeyCol'
        '201':
          description: Created
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupKeyCol'
        '204':
          description: No Content
    delete:
      tags:
        - lookup-key-col-entity-controller
      description: delete-lookupkeycol
      operationId: deleteItemResource-lookupkeycol-delete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No Content
        '404':
          description: Not Found
    patch:
      tags:
        - lookup-key-col-entity-controller
      description: patch-lookupkeycol
      operationId: patchItemResource-lookupkeycol-patch
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LookupKeyColRequestBody'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupKeyCol'
        '204':
          description: No Content
  /lookupMetadata:
    get:
      tags:
        - lookup-metadata-entity-controller
      description: get-lookupmetadata
      operationId: getCollectionResource-lookupmetadata-get_1
      parameters:
        - name: page
          in: query
          description: Zero-based page index (0..N)
          required: false
          schema:
            minimum: 0
            type: integer
            default: 0
        - name: size
          in: query
          description: The size of the page to be returned
          required: false
          schema:
            minimum: 1
            type: integer
            default: 20
        - name: sort
          in: query
          description: 'Sorting criteria in the format: property,(asc|desc). Default sort order is ascending. Multiple sort criteria are supported.'
          required: false
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/PagedModelEntityModelLookupMetadata'
            application/x-spring-data-compact+json:
              schema:
                $ref: '#/components/schemas/PagedModelEntityModelLookupMetadata'
            text/uri-list:
              schema:
                type: string
    post:
      tags:
        - lookup-metadata-entity-controller
      description: create-lookupmetadata
      operationId: postCollectionResource-lookupmetadata-post
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LookupMetadataRequestBody'
        required: true
      responses:
        '201':
          description: Created
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupMetadata'
  /lookupMetadata/search/findByLookupName:
    get:
      tags:
        - lookup-metadata-search-controller
      operationId: executeSearch-lookupmetadata-get
      parameters:
        - name: lookupName
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupMetadata'
        '404':
          description: Not Found
  /lookupMetadata/search/searchLookupMetadata:
    get:
      tags:
        - lookup-metadata-search-controller
      operationId: executeSearch-lookupmetadata-get_1
      parameters:
        - name: name
          in: query
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/CollectionModelEntityModelLookupMetadata'
        '404':
          description: Not Found
  /lookupMetadata/{id}:
    get:
      tags:
        - lookup-metadata-entity-controller
      description: get-lookupmetadata
      operationId: getItemResource-lookupmetadata-get
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupMetadata'
        '404':
          description: Not Found
    put:
      tags:
        - lookup-metadata-entity-controller
      description: update-lookupmetadata
      operationId: putItemResource-lookupmetadata-put
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LookupMetadataRequestBody'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupMetadata'
        '201':
          description: Created
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupMetadata'
        '204':
          description: No Content
    delete:
      tags:
        - lookup-metadata-entity-controller
      description: delete-lookupmetadata
      operationId: deleteItemResource-lookupmetadata-delete
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No Content
        '404':
          description: Not Found
    patch:
      tags:
        - lookup-metadata-entity-controller
      description: patch-lookupmetadata
      operationId: patchItemResource-lookupmetadata-patch
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LookupMetadataRequestBody'
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/EntityModelLookupMetadata'
        '204':
          description: No Content
  /profile:
    get:
      tags:
        - profile-controller
      operationId: listAllFormsOfMetadata_1
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                $ref: '#/components/schemas/RepresentationModelObject'
  /profile/cicClaimcheckses:
    get:
      tags:
        - profile-controller
      operationId: descriptor_1_1_1
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: string
            application/alps+json:
              schema:
                type: string
            application/schema+json:
              schema:
                $ref: '#/components/schemas/JsonSchema'
  /profile/lookupKeyCol:
    get:
      tags:
        - profile-controller
      operationId: descriptor_1_1_2
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: string
            application/alps+json:
              schema:
                type: string
            application/schema+json:
              schema:
                $ref: '#/components/schemas/JsonSchema'
  /profile/lookupMetadata:
    get:
      tags:
        - profile-controller
      operationId: descriptor_1_1_3
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                type: string
            application/alps+json:
              schema:
                type: string
            application/schema+json:
              schema:
                $ref: '#/components/schemas/JsonSchema'
  /lookupData/{lookupName}:
    put:
      tags:
        - lookup-controller
      summary: Lookup Data
      description: lookupData
      operationId: updateLookupData
      parameters:
        - name: lookupName
          in: path
          required: true
          schema:
            type: string
        - name: userId
          in: query
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: object
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
    post:
      tags:
        - lookup-controller
      summary: Lookup Data
      description: lookupData
      operationId: addLookupData
      parameters:
        - name: lookupName
          in: path
          required: true
          schema:
            type: string
        - name: userId
          in: query
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: object
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
    delete:
      tags:
        - lookup-controller
      summary: Lookup Data
      description: lookupData
      operationId: deleteLookupData
      parameters:
        - name: lookupName
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: object
        required: true
      responses:
        '200':
          description: OK
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
  /addLookup:
    post:
      tags:
        - lookup-metadata-controller
      summary: Lookup Data
      description: addLookup
      operationId: addLookup
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: object
        required: true
      responses:
        '200':
          description: Success
          content:
            application/hal+json:
              schema:
                type: string
        '401':
          description: Unauthorized
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
        '403':
          description: Forbidden
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
        '404':
          description: Not Found
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
        '500':
          description: Failure
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
  /getLookups:
    get:
      tags:
        - lookup-metadata-controller
      summary: Lookup Data
      description: getLookups
      operationId: getLookups
      parameters:
        - name: searchParam
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/hal+json:
              schema:
                type: string
        '401':
          description: Unauthorized
          content:
            application/hal+json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: object
        '403':
          description: Forbidden
          content:
            application/hal+json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: object
        '404':
          description: Not Found
          content:
            application/hal+json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: object
        '500':
          description: Failure
          content:
            application/hal+json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: object
  /getLookupData:
    get:
      tags:
        - lookup-metadata-controller
      summary: Lookup Data
      description: getLookupData
      operationId: getLookupData
      parameters:
        - name: lookupName
          in: query
          description: Look up Name
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/hal+json:
              schema:
                type: string
        '401':
          description: Unauthorized
          content:
            application/hal+json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: object
        '403':
          description: Forbidden
          content:
            application/hal+json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: object
        '404':
          description: Not Found
          content:
            application/hal+json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: object
        '500':
          description: Failure
          content:
            application/hal+json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: object
  /getClaimChecks/{claimCheckId}:
    get:
      tags:
        - claim-check-controller
      summary: getClaimCheckPayload
      description: getPayload
      operationId: getClaimCheckpayload
      parameters:
        - name: claimCheckId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: success
          content:
            application/xml:
              schema:
                type: string
            text/xml:
              schema:
                type: string
        '401':
          description: Unauthorized
          content:
            application/xml:
              schema:
                type: string
            text/xml:
              schema:
                type: string
        '403':
          description: Forbidden
          content:
            application/xml:
              schema:
                type: string
            text/xml:
              schema:
                type: string
        '404':
          description: Not Found
          content:
            application/xml:
              schema:
                type: string
            text/xml:
              schema:
                type: string
        '500':
          description: Failure
          content:
            application/xml:
              schema:
                type: string
            text/xml:
              schema:
                type: string
  /deleteLookup/{lookupName}:
    delete:
      tags:
        - lookup-metadata-controller
      summary: Lookup Data
      description: deleteLookup
      operationId: deleteLookup
      parameters:
        - name: lookupName
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/hal+json:
              schema:
                type: string
        '401':
          description: Unauthorized
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
        '403':
          description: Forbidden
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
        '404':
          description: Not Found
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
        '500':
          description: Failure
          content:
            application/hal+json:
              schema:
                type: string
                enum:
                  - 100 CONTINUE
                  - 101 SWITCHING_PROTOCOLS
                  - 102 PROCESSING
                  - 103 EARLY_HINTS
                  - 103 CHECKPOINT
                  - 200 OK
                  - 201 CREATED
                  - 202 ACCEPTED
                  - 203 NON_AUTHORITATIVE_INFORMATION
                  - 204 NO_CONTENT
                  - 205 RESET_CONTENT
                  - 206 PARTIAL_CONTENT
                  - 207 MULTI_STATUS
                  - 208 ALREADY_REPORTED
                  - 226 IM_USED
                  - 300 MULTIPLE_CHOICES
                  - 301 MOVED_PERMANENTLY
                  - 302 FOUND
                  - 302 MOVED_TEMPORARILY
                  - 303 SEE_OTHER
                  - 304 NOT_MODIFIED
                  - 305 USE_PROXY
                  - 307 TEMPORARY_REDIRECT
                  - 308 PERMANENT_REDIRECT
                  - 400 BAD_REQUEST
                  - 401 UNAUTHORIZED
                  - 402 PAYMENT_REQUIRED
                  - 403 FORBIDDEN
                  - 404 NOT_FOUND
                  - 405 METHOD_NOT_ALLOWED
                  - 406 NOT_ACCEPTABLE
                  - 407 PROXY_AUTHENTICATION_REQUIRED
                  - 408 REQUEST_TIMEOUT
                  - 409 CONFLICT
                  - 410 GONE
                  - 411 LENGTH_REQUIRED
                  - 412 PRECONDITION_FAILED
                  - 413 PAYLOAD_TOO_LARGE
                  - 413 REQUEST_ENTITY_TOO_LARGE
                  - 414 URI_TOO_LONG
                  - 414 REQUEST_URI_TOO_LONG
                  - 415 UNSUPPORTED_MEDIA_TYPE
                  - 416 REQUESTED_RANGE_NOT_SATISFIABLE
                  - 417 EXPECTATION_FAILED
                  - 418 I_AM_A_TEAPOT
                  - 419 INSUFFICIENT_SPACE_ON_RESOURCE
                  - 420 METHOD_FAILURE
                  - 421 DESTINATION_LOCKED
                  - 422 UNPROCESSABLE_ENTITY
                  - 423 LOCKED
                  - 424 FAILED_DEPENDENCY
                  - 425 TOO_EARLY
                  - 426 UPGRADE_REQUIRED
                  - 428 PRECONDITION_REQUIRED
                  - 429 TOO_MANY_REQUESTS
                  - 431 REQUEST_HEADER_FIELDS_TOO_LARGE
                  - 451 UNAVAILABLE_FOR_LEGAL_REASONS
                  - 500 INTERNAL_SERVER_ERROR
                  - 501 NOT_IMPLEMENTED
                  - 502 BAD_GATEWAY
                  - 503 SERVICE_UNAVAILABLE
                  - 504 GATEWAY_TIMEOUT
                  - 505 HTTP_VERSION_NOT_SUPPORTED
                  - 506 VARIANT_ALSO_NEGOTIATES
                  - 507 INSUFFICIENT_STORAGE
                  - 508 LOOP_DETECTED
                  - 509 BANDWIDTH_LIMIT_EXCEEDED
                  - 510 NOT_EXTENDED
                  - 511 NETWORK_AUTHENTICATION_REQUIRED
components:
  schemas:
    AbstractJsonSchemaPropertyObject:
      type: object
      properties:
        title:
          type: string
        readOnly:
          type: boolean
    Item:
      type: object
      properties:
        type:
          type: string
        properties:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/AbstractJsonSchemaPropertyObject'
        requiredProperties:
          type: array
          items:
            type: string
    JsonSchema:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
        properties:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/AbstractJsonSchemaPropertyObject'
        requiredProperties:
          type: array
          items:
            type: string
        definitions:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Item'
        type:
          type: string
        $schema:
          type: string
    Links:
      type: object
      additionalProperties:
        $ref: '#/components/schemas/Link'
    RepresentationModelObject:
      type: object
      properties:
        _links:
          $ref: '#/components/schemas/Links'
    CollectionModelEntityModelCicClaimchecks:
      type: object
      properties:
        _embedded:
          type: object
          properties:
            cicClaimcheckses:
              type: array
              items:
                $ref: '#/components/schemas/EntityModelCicClaimchecks'
        _links:
          $ref: '#/components/schemas/Links'
    EntityModelCicClaimchecks:
      type: object
      properties:
        payload:
          type: array
          items:
            type: string
            format: byte
        created:
          type: string
          format: date-time
        _links:
          $ref: '#/components/schemas/Links'
    EntityModelLookupMetadata:
      type: object
      properties:
        tableName:
          type: string
        sql:
          type: string
        _links:
          $ref: '#/components/schemas/Links'
    PageMetadata:
      type: object
      properties:
        size:
          type: integer
          format: int64
        totalElements:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int64
        number:
          type: integer
          format: int64
    PagedModelEntityModelLookupMetadata:
      type: object
      properties:
        _embedded:
          type: object
          properties:
            lookupMetadatas:
              type: array
              items:
                $ref: '#/components/schemas/EntityModelLookupMetadata'
        _links:
          $ref: '#/components/schemas/Links'
        page:
          $ref: '#/components/schemas/PageMetadata'
    CollectionModelEntityModelLookupMetadata:
      type: object
      properties:
        _embedded:
          type: object
          properties:
            lookupMetadatas:
              type: array
              items:
                $ref: '#/components/schemas/EntityModelLookupMetadata'
        _links:
          $ref: '#/components/schemas/Links'
    EntityModelLookupKeyCol:
      type: object
      properties:
        lookupName:
          type: string
        columnAlias:
          type: string
        _links:
          $ref: '#/components/schemas/Links'
    PagedModelEntityModelLookupKeyCol:
      type: object
      properties:
        _embedded:
          type: object
          properties:
            lookupKeyCols:
              type: array
              items:
                $ref: '#/components/schemas/EntityModelLookupKeyCol'
        _links:
          $ref: '#/components/schemas/Links'
        page:
          $ref: '#/components/schemas/PageMetadata'
    CollectionModelEntityModelLookupKeyCol:
      type: object
      properties:
        _embedded:
          type: object
          properties:
            lookupKeyCols:
              type: array
              items:
                $ref: '#/components/schemas/EntityModelLookupKeyCol'
        _links:
          $ref: '#/components/schemas/Links'
    CicClaimchecksRequestBody:
      type: object
      properties:
        id:
          type: string
        payload:
          type: array
          items:
            type: string
            format: byte
        created:
          type: string
          format: date-time
    LookupKeyColRequestBody:
      type: object
      properties:
        id:
          type: integer
          format: int32
        lookupName:
          type: string
        columnAlias:
          type: string
    LookupMetadataRequestBody:
      type: object
      properties:
        lookupName:
          type: string
        tableName:
          type: string
        sql:
          type: string
    Link:
      type: object
      properties:
        href:
          type: string
        hreflang:
          type: string
        title:
          type: string
        type:
          type: string
        deprecation:
          type: string
        profile:
          type: string
        name:
          type: string
        templated:
          type: boolean
