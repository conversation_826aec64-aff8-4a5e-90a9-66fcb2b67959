* text=auto !eol
/pom.xml -text
src/main/cf/manifest/manifest.yml -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/Application.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/CicadmConfiguration.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/CictransConfiguration.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/GemfireConfiguration.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/GemfireLookupServletInitializer.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/LookupConfiguration.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/PathMatchConfiguration.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/SecurityConfig.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/cicadm/LookupKeyCol.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/cicadm/LookupKeyColRepository.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/cicadm/LookupMetadata.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/cicadm/LookupMetadataRepository.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/cicadm/LookupMetadataService.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/cictrans/LookupService.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/controller/ClaimCheckController.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/controller/LookupController.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/controller/LookupMetadataController.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/gemfire/CicClaimchecks.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/gemfire/ClaimCheckRepository.java -text
src/main/java/com/emc/it/eis/gemfire/lookup/service/utils/JsonHelper.java -text
src/main/resources/application-context.xml -text
src/main/resources/bootstrap.yml -text
src/main/resources/db-config.xml -text
src/main/resources/db.properties -text
src/main/resources/log4j2.xml -text
src/test/java/com/emc/it/eis/gemfire/lookup/service/test/ClaimCheckControllerTests.java -text
src/test/java/com/emc/it/eis/gemfire/lookup/service/test/LookupControllerTests.java -text
src/test/java/com/emc/it/eis/gemfire/lookup/service/test/LookupMetadataControllerTests.java -text
src/test/resources/claimChecks.xml -text
src/test/resources/db-test.properties -text
