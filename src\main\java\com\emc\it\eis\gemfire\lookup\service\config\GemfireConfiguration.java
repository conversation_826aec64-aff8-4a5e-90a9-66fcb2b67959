package com.emc.it.eis.gemfire.lookup.service.config;

import java.util.HashMap;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@EnableJpaRepositories(basePackages = "com.emc.it.eis.gemfire.lookup.service.gemfire", entityManagerFactoryRef = "gemfireEntityManager", transactionManagerRef = "gemfireTransactionManager")
public class GemfireConfiguration {

	
	
	@Autowired
	@Qualifier(value = "gemfire.dataSource")
	DataSource gemfireDataSource;
	
	private String hibernateDialect = "org.hibernate.dialect.OracleDialect";

	private String hbm2ddlValue = "validate";

	@Bean
	public LocalContainerEntityManagerFactoryBean gemfireEntityManager() {
		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(gemfireDataSource);
		em.setPackagesToScan(new String[]{"com.emc.it.eis.gemfire.lookup.service.gemfire"});

		HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		HashMap<String, Object> properties = new HashMap<>();
		properties.put("hibernate.hbm2ddl.validate", hbm2ddlValue);
		properties.put("hibernate.dialect", hibernateDialect);
		em.setJpaPropertyMap(properties);

		return em;
	}

	@Bean
	public PlatformTransactionManager gemfireTransactionManager() {
		JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(gemfireEntityManager().getObject());
		return transactionManager;
	}

	@Bean
	public JdbcTemplate gemfireJdbcTemplate() {
		JdbcTemplate jdbcTemplate = new JdbcTemplate();
		jdbcTemplate.setDataSource(gemfireDataSource);
		return jdbcTemplate;
	}
}
