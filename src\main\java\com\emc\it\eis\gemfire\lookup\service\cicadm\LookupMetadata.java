package com.emc.it.eis.gemfire.lookup.service.cicadm;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "CIC_LOOKUP_METADATA")
public class LookupMetadata {

	@Id
	@Column(name="lookup_name")
	private String lookupName;
	@Column(name="table_name")
	private String tableName;
	@Column(name="sql")
	private String sql;

	public String getLookupName() {
		return lookupName;
	}

	public void setLookupName(String lookupName) {
		this.lookupName = lookupName;
	}

	public String getTableName() {
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getSql() {
		return sql;
	}

	public void setSql(String sql) {
		this.sql = sql;
	}

}
