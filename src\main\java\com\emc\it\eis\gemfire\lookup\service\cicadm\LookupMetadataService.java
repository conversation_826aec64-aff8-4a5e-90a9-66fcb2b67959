package com.emc.it.eis.gemfire.lookup.service.cicadm;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.owasp.esapi.Encoder;
import org.owasp.esapi.codecs.OracleCodec;
import org.owasp.esapi.reference.DefaultEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;

@Service
public class LookupMetadataService {

	@Autowired
	private JdbcTemplate cicadmJdbcTemplate;

	@Autowired
	private LookupMetadataRepository lookupMetadataRepository;

	@Autowired
	private LookupKeyColRepository lookupKeyColRepository;

	private static Logger logger = LoggerFactory
			.getLogger(LookupMetadataService.class);

	private static final String LOOKUP_KEY_COLUMNS = "lookupKeysColumns";

	@Transactional("cicadmTransactionManager")
	public List<Map<String, Object>> getLookupData(String sqlQuery) {
		Encoder esapiEncoder =  DefaultEncoder.getInstance();
		String  sanitizedInsertQuery = esapiEncoder.encodeForSQL(new OracleCodec(), sqlQuery);
		logger.info("In getting the look up data for sql: {}", sqlQuery);
		List<Map<String, Object>> dataList = cicadmJdbcTemplate
				.queryForList(sanitizedInsertQuery);
		if (dataList.isEmpty()) {
			Map<String, Object> dataMap = cicadmJdbcTemplate.query(sanitizedInsertQuery,
					new ResultSetExtractor<>() {
						@Override
						public Map<String, Object> extractData(ResultSet rs)
								throws SQLException, DataAccessException {
							ResultSetMetaData rsmd = rs.getMetaData();
							int columnCount = rsmd.getColumnCount();
							Map<String, Object> columns = new HashMap<>();
							for (int i = 1; i <= columnCount; i++) {
								String columnName = rsmd.getColumnName(i);
								columns.put(columnName, "");
							}
							return columns;
						}
					});
			dataList.add(dataMap);
		
		}
		return dataList;
	}

	@SuppressWarnings("unchecked")
	@Transactional("cicadmTransactionManager")
	public List<Map<String, Object>> getLookups(String lookupName) {
		logger.info("In getting the lookups: {}", lookupName);
		Iterable<LookupMetadata> lookups = null;
		ObjectMapper mapper = new ObjectMapper();
		List<Map<String, Object>> listMap = null;
		String  sanitizedInsertQuery = null;

		if (lookupName == null) {
			lookups = lookupMetadataRepository.findAll();
		} else {
			logger.info("Getting lookups based on search criteria: {}", lookupName);
			lookups = lookupMetadataRepository.searchLookupMetadata(lookupName);
			
		}
		if (lookups != null) {
			Iterator<LookupMetadata> iterator = lookups.iterator();
			 listMap = new ArrayList<>();
			while (iterator.hasNext()) {
				LookupMetadata lookupMetadata = iterator.next();
				if (!("APPLICATION_RULES"
						.equals(lookupMetadata.getLookupName()) || "COMMON_LOOKUP".equals(lookupMetadata
						.getLookupName()))) {

					Map<String, Object> map = mapper.convertValue(
							lookupMetadata, Map.class);
					List<LookupKeyCol> lookupKeyCols = lookupKeyColRepository
							.getKeyCols(lookupMetadata.getLookupName());
					List<String> keyList = new ArrayList<>();
					for (LookupKeyCol lookupKeyCol : lookupKeyCols) {
						keyList.add(lookupKeyCol.getColumnAlias());
					}
					map.put(LOOKUP_KEY_COLUMNS, keyList);
					listMap.add(map);
					
				} else {
					logger.info("Some of the look ups are not added to the map: ");
				}
			}
			logger.info("Returning the lookup results {}", lookupName);
			
		} else {
			logger.info("Returning the lookup results {}", lookupName);
			
		}
		return listMap;
	}

	@Transactional("cicadmTransactionManager")
	public HttpStatus addLookup(Map<String, Object> lookupData) {
		logger.info("In adding/updating the lookup {}", lookupData);
		Map<String, Object> metadataMap = new HashMap<>();

		String keyString = (String) lookupData.get(LOOKUP_KEY_COLUMNS);
		List<String> keys = Arrays.asList(keyString.split(","));
		metadataMap.putAll(lookupData);
		metadataMap.remove(LOOKUP_KEY_COLUMNS);
		metadataMap.remove("r");

		ObjectMapper mapper = new ObjectMapper();
		LookupMetadata lookupMetadata = mapper.convertValue(metadataMap,
				LookupMetadata.class);
		lookupMetadataRepository.save(lookupMetadata);

		List<LookupKeyCol> lookupKeyCols = lookupKeyColRepository
				.getKeyCols(lookupMetadata.getLookupName());
		for (LookupKeyCol lookupKeyCol : lookupKeyCols) {
			lookupKeyColRepository.delete(lookupKeyCol);
		}

		for (String key : keys) {
			LookupKeyCol lookupKeyCol = new LookupKeyCol();
			lookupKeyCol.setLookupName(lookupMetadata.getLookupName());
			lookupKeyCol.setColumnAlias(key);
			lookupKeyColRepository.save(lookupKeyCol);
		}
		logger.info("In adding/updating the lookup: Lookup added successfully {}", lookupMetadata.getLookupName());
		return HttpStatus.CREATED;
	}

	@Transactional("cicadmTransactionManager")
	public HttpStatus deleteLookup(String lookupName) {
		logger.info("In deleting the lookup {}", lookupName);
		List<LookupKeyCol> lookupKeyCols = lookupKeyColRepository
				.getKeyCols(lookupName);
		for (LookupKeyCol lookupKeyCol : lookupKeyCols) {
			lookupKeyColRepository.delete(lookupKeyCol);
		}

		LookupMetadata lookupMetadata = lookupMetadataRepository
				.findByLookupName(lookupName);
		lookupMetadataRepository.delete(lookupMetadata);
		logger.info("In deleting the lookup : Lookup deleed {}", lookupName);
		return HttpStatus.ACCEPTED;
	}
}
