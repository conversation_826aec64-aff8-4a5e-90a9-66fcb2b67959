package com.emc.it.eis.gemfire.lookup.service.utils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

@SuppressWarnings({"rawtypes", "unchecked"})
public final class JsonHelper {
	private JsonHelper() {
	    throw new IllegalStateException("JsonHelper class");
	  }

	public static Object toJSON(Object object) throws JSONException {
		if (object instanceof Map map) {
			JSONObject json = new JSONObject();
			for (Object key : map.entrySet()) {
				json.put(key.toString(), toJSON(map.get(key)));
			}
			return json;
		} else if (object instanceof Iterable iterable) {
			JSONArray json = new JSONArray();
			for (Object value :iterable) {
				json.put(value);
			}
			return json;
		} else {
			return object;
		}
	}

	public static boolean isEmptyObject(JSONObject object) {
		return object.names() == null;
	}

	public static Map<String, Object> getMap(JSONObject object, String key)
			throws JSONException {
		return toMap(object.getJSONObject(key));
	}

	public static Map<String, Object> toMap(JSONObject object)
			throws JSONException {
		Map<String, Object> map = new HashMap();
		Iterator keys = object.keys();
		while (keys.hasNext()) {
			String key = (String) keys.next();
			map.put(key, fromJson(object.get(key)));
		}
		return map;
	}

	public static List toList(JSONArray array) throws JSONException {
		List list = new ArrayList();
		for (int i = 0; i < array.length(); i++) {
			list.add(fromJson(array.get(i)));
		}
		return list;
	}

	public static Object fromJson(Object json) throws JSONException {
		if (json == JSONObject.NULL) {
			return null;
		} else if (json instanceof JSONObject object) {
			return toMap(object);
		} else if (json instanceof JSONArray array) {
			return toList(array);
		} else {
			return json;
		}
	}
}
