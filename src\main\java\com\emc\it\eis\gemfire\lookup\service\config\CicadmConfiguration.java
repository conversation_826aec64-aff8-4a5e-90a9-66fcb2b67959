package com.emc.it.eis.gemfire.lookup.service.config;

import java.util.HashMap;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@EnableJpaRepositories(basePackages = "com.emc.it.eis.gemfire.lookup.service.cicadm", entityManagerFactoryRef = "cicadmEntityManager", transactionManagerRef = "cicadmTransactionManager")
public class CicadmConfiguration {

	@Autowired
	@Qualifier(value = "cicadm.dataSource")
	DataSource cicadmDataSource;

	private String hibernateDialect = "org.hibernate.dialect.OracleDialect";

	private String hbm2ddlValue = "validate";

	@Bean
	public LocalContainerEntityManagerFactoryBean cicadmEntityManager() {
		LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(cicadmDataSource);
		em.setPackagesToScan(new String[]{"com.emc.it.eis.gemfire.lookup.service.cicadm"});

		HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		HashMap<String, Object> properties = new HashMap<>();
		properties.put("hibernate.hbm2ddl.validate", hbm2ddlValue);
		properties.put("hibernate.dialect", hibernateDialect);
		em.setJpaPropertyMap(properties);

		return em;
	}

	@Bean
	public PlatformTransactionManager cicadmTransactionManager() {
		JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(cicadmEntityManager().getObject());
		return transactionManager;
	}

	@Bean
	public JdbcTemplate cicadmJdbcTemplate() {
		JdbcTemplate jdbcTemplate = new JdbcTemplate();
		jdbcTemplate.setDataSource(cicadmDataSource);
		return jdbcTemplate;
	}
}
