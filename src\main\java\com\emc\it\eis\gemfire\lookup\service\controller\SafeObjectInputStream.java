package com.emc.it.eis.gemfire.lookup.service.controller;

import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectStreamClass;
import java.util.HashMap;
import java.util.UUID;

public class SafeObjectInputStream extends ObjectInputStream {
	public SafeObjectInputStream(InputStream in) throws IOException {
		super(in);

	}

	@Override
	protected Class<?> resolveClass(ObjectStreamClass desc) throws IOException, ClassNotFoundException {
		if (!desc.getName().equals(HashMap.class.getName()) && !desc.getName().equals(UUID.class.getName())
				&& !desc.getName().equals(Long.class.getName()) && !desc.getName().equals(Number.class.getName())) {
			throw new ClassNotFoundException("Unsupported Class: " + desc.getName());
		}
		return super.resolveClass(desc);
	}

}
