
package com.emc.it.eis.gemfire.lookup.service.config;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import
org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;

@Configuration
public class SwaggerConfiguration {
	
	@Bean
	public GroupedOpenApi publicApi() {
		return GroupedOpenApi.builder().group("public").pathsToMatch("/*.*").build();
	}
	
	@Bean
	public OpenAPI openAPI() {
		return new OpenAPI().info(new Info().title("Gemfire Lookup Service")
				.description("Api for Gemfire Lookup Service").version("1.0").license(new License()
						.name("Apache License Version 2.0").url("https://www.apache.org/licenses/LICENSE-2.0")));
	}
	}
