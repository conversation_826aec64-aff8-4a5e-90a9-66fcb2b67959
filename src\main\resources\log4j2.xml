<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xml>
<Configuration status="info" packages="io.pivotal.cloudfoundry.log4j">

	<Appenders>
		<Console name="STDOUT" target="SYSTEM_OUT">
			<PatternLayout
				pattern="[%d{dd.MM.yyyy HH:mm:ss}] [%t] [CF-SpaceName:${cf:space_name}]/[CF-AppName:${cf:application_name}] %p: %c.%M() - %m%n" />
		</Console>
	</Appenders>

	<Loggers>
		<Logger name="org.springframework" level="info" />
		<Logger name="org.codehaus.jackson" level="info" />
		<Logger name="org.hibernate" level="info" />
		<Logger name="com.emc.it.eis" level="info" />
		<Root level="info">
			<AppenderRef ref="STDOUT" />
		</Root>
	</Loggers>

</Configuration>
